'use client'

import { useState, useRef } from 'react'
import { 
  CloudArrowDownIcon, 
  CloudArrowUpIcon, 
  DocumentArrowDownIcon,
  TrashIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import { 
  createSecureBackup, 
  restoreSecureBackup, 
  downloadBackup, 
  readBackupFile,
  getStoredBackups,
  deleteBackup,
  BackupMetadata 
} from '@/lib/backup'
import { useToastContext } from '@/contexts/ToastContext'

export default function BackupManager() {
  const [backups, setBackups] = useState<BackupMetadata[]>([])
  const [isCreating, setIsCreating] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const [showBackups, setShowBackups] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)
  const { success, error, info } = useToastContext()

  const loadBackups = () => {
    const storedBackups = getStoredBackups()
    setBackups(storedBackups)
  }

  const handleCreateBackup = async () => {
    setIsCreating(true)
    try {
      const result = await createSecureBackup()
      if (result.success && result.backupData && result.metadata) {
        success('تم إنشاء النسخة الاحتياطية بنجاح')
        
        // تحميل النسخة الاحتياطية تلقائياً
        const filename = `${result.metadata.name.replace(/\s+/g, '-')}-${result.metadata.id}.json`
        downloadBackup(result.backupData, filename)
        
        loadBackups()
      } else {
        error(result.message)
      }
    } catch (err) {
      error('فشل في إنشاء النسخة الاحتياطية')
    } finally {
      setIsCreating(false)
    }
  }

  const handleFileSelect = () => {
    fileInputRef.current?.click()
  }

  const handleFileChange = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    if (!file.name.endsWith('.json')) {
      error('يرجى اختيار ملف JSON صحيح')
      return
    }

    setIsRestoring(true)
    try {
      const backupContent = await readBackupFile(file)
      const result = await restoreSecureBackup(backupContent)
      
      if (result.success) {
        success('تم استعادة النسخة الاحتياطية بنجاح')
        info('سيتم إعادة تحميل الصفحة لتطبيق التغييرات')
        
        // إعادة تحميل الصفحة بعد ثانيتين
        setTimeout(() => {
          window.location.reload()
        }, 2000)
      } else {
        error(result.message)
      }
    } catch (err) {
      error('فشل في قراءة أو استعادة النسخة الاحتياطية')
    } finally {
      setIsRestoring(false)
      // إعادة تعيين input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    }
  }

  const handleDeleteBackup = (backupId: string) => {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟')) {
      if (deleteBackup(backupId)) {
        success('تم حذف النسخة الاحتياطية')
        loadBackups()
      } else {
        error('فشل في حذف النسخة الاحتياطية')
      }
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString('ar-SA')
  }

  return (
    <div className="bg-white rounded-lg shadow-sm border p-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <ShieldCheckIcon className="w-6 h-6 text-primary-600 ml-3" />
          <h2 className="text-xl font-semibold text-gray-900">إدارة النسخ الاحتياطية</h2>
        </div>
        <button
          onClick={() => {
            setShowBackups(!showBackups)
            if (!showBackups) loadBackups()
          }}
          className="text-sm text-primary-600 hover:text-primary-800"
        >
          {showBackups ? 'إخفاء' : 'عرض'} النسخ المحفوظة
        </button>
      </div>

      {/* أزرار العمليات الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
        <button
          onClick={handleCreateBackup}
          disabled={isCreating}
          className="flex items-center justify-center px-4 py-3 bg-primary-600 text-white rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isCreating ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
              جاري الإنشاء...
            </>
          ) : (
            <>
              <CloudArrowDownIcon className="w-5 h-5 ml-2" />
              إنشاء نسخة احتياطية
            </>
          )}
        </button>

        <button
          onClick={handleFileSelect}
          disabled={isRestoring}
          className="flex items-center justify-center px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
        >
          {isRestoring ? (
            <>
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white ml-2"></div>
              جاري الاستعادة...
            </>
          ) : (
            <>
              <CloudArrowUpIcon className="w-5 h-5 ml-2" />
              استعادة نسخة احتياطية
            </>
          )}
        </button>
      </div>

      {/* input مخفي لاختيار الملف */}
      <input
        ref={fileInputRef}
        type="file"
        accept=".json"
        onChange={handleFileChange}
        className="hidden"
      />

      {/* تحذير أمني */}
      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
        <div className="flex">
          <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400 ml-2 mt-0.5" />
          <div className="text-sm text-yellow-800">
            <p className="font-medium mb-1">تنبيه أمني:</p>
            <ul className="list-disc list-inside space-y-1">
              <li>النسخ الاحتياطية مشفرة بقوة لحماية بياناتك</li>
              <li>احتفظ بالنسخ الاحتياطية في مكان آمن</li>
              <li>لا تشارك ملفات النسخ الاحتياطية مع أشخاص غير موثوقين</li>
              <li>تأكد من صحة الملف قبل الاستعادة</li>
            </ul>
          </div>
        </div>
      </div>

      {/* قائمة النسخ الاحتياطية المحفوظة */}
      {showBackups && (
        <div className="border-t pt-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">النسخ الاحتياطية المحفوظة</h3>
          
          {backups.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <DocumentArrowDownIcon className="w-12 h-12 mx-auto mb-3 text-gray-300" />
              <p>لا توجد نسخ احتياطية محفوظة</p>
            </div>
          ) : (
            <div className="space-y-3">
              {backups.map((backup) => (
                <div key={backup.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <h4 className="font-medium text-gray-900">{backup.name}</h4>
                    <div className="text-sm text-gray-500 mt-1">
                      <span>تاريخ الإنشاء: {formatDate(backup.timestamp)}</span>
                      <span className="mx-2">•</span>
                      <span>الحجم: {formatFileSize(backup.size)}</span>
                      <span className="mx-2">•</span>
                      <span>الإصدار: {backup.version}</span>
                      {backup.encrypted && (
                        <>
                          <span className="mx-2">•</span>
                          <span className="text-green-600">مشفر</span>
                        </>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleDeleteBackup(backup.id)}
                      className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-colors"
                      title="حذف النسخة الاحتياطية"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  )
}
