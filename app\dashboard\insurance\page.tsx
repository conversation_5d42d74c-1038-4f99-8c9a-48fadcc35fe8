'use client'

import { useState } from 'react'
import {
  ShieldCheckIcon,
  HeartIcon,
  HomeIcon,
  TruckIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon
} from '@heroicons/react/24/outline'

export default function InsurancePage() {
  const [policies] = useState([
    {
      id: '1',
      name: 'تأمين صحي شامل',
      type: 'صحي',
      provider: 'شركة بوبا العربية',
      premium: 8000,
      coverage: 500000,
      startDate: '2024-01-01',
      endDate: '2024-12-31',
      status: 'active',
      icon: HeartIcon,
      color: 'red'
    },
    {
      id: '2',
      name: 'تأمين السيارة الشامل',
      type: 'مركبات',
      provider: 'شركة التعاونية للتأمين',
      premium: 3500,
      coverage: 150000,
      startDate: '2024-03-15',
      endDate: '2025-03-14',
      status: 'active',
      icon: TruckIcon,
      color: 'blue'
    },
    {
      id: '3',
      name: 'تأمين المنزل',
      type: 'ممتلكات',
      provider: 'شركة الأهلي للتكافل',
      premium: 2000,
      coverage: 800000,
      startDate: '2023-12-01',
      endDate: '2024-11-30',
      status: 'expiring',
      icon: HomeIcon,
      color: 'green'
    }
  ])

  const totalPremiums = policies.reduce((sum, policy) => sum + policy.premium, 0)
  const totalCoverage = policies.reduce((sum, policy) => sum + policy.coverage, 0)
  const activePolicies = policies.filter(p => p.status === 'active').length
  const expiringPolicies = policies.filter(p => p.status === 'expiring').length

  const getStatusConfig = (status: string) => {
    const configs = {
      active: { label: 'نشط', color: 'text-green-600 bg-green-50', icon: CheckCircleIcon },
      expiring: { label: 'ينتهي قريباً', color: 'text-yellow-600 bg-yellow-50', icon: ExclamationTriangleIcon },
      expired: { label: 'منتهي', color: 'text-red-600 bg-red-50', icon: ExclamationTriangleIcon }
    }
    return configs[status as keyof typeof configs] || configs.active
  }

  const getColorClasses = (color: string) => {
    const colors = {
      red: 'border-red-200 bg-red-50',
      blue: 'border-blue-200 bg-blue-50',
      green: 'border-green-200 bg-green-50',
      purple: 'border-purple-200 bg-purple-50'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  const getDaysUntilExpiry = (endDate: string) => {
    const today = new Date()
    const expiry = new Date(endDate)
    const diffTime = expiry.getTime() - today.getTime()
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    return diffDays
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة التأمين</h1>
          <p className="text-gray-600">تتبع وإدارة بوالص التأمين الخاصة بك</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          بوليصة جديدة
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">البوالص النشطة</p>
              <p className="text-2xl font-bold text-green-600">{activePolicies}</p>
            </div>
            <ShieldCheckIcon className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">تنتهي قريباً</p>
              <p className="text-2xl font-bold text-yellow-600">{expiringPolicies}</p>
            </div>
            <ExclamationTriangleIcon className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الأقساط</p>
              <p className="text-2xl font-bold text-blue-600">{totalPremiums.toLocaleString()} ر.س</p>
            </div>
            <CurrencyDollarIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي التغطية</p>
              <p className="text-2xl font-bold text-purple-600">{(totalCoverage / 1000000).toFixed(1)}م ر.س</p>
            </div>
            <ShieldCheckIcon className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {policies.map((policy) => {
          const IconComponent = policy.icon
          const statusConfig = getStatusConfig(policy.status)
          const StatusIcon = statusConfig.icon
          const daysUntilExpiry = getDaysUntilExpiry(policy.endDate)
          
          return (
            <div key={policy.id} className={`bg-white rounded-lg shadow-sm border-2 ${getColorClasses(policy.color)} p-6`}>
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(policy.color)}`}>
                    <IconComponent className="w-6 h-6 text-gray-700" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{policy.name}</h3>
                    <p className="text-sm text-gray-500">{policy.type}</p>
                  </div>
                </div>
                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusConfig.color}`}>
                  <StatusIcon className="w-3 h-3 ml-1" />
                  {statusConfig.label}
                </span>
              </div>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">شركة التأمين:</span>
                  <span className="font-medium">{policy.provider}</span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">القسط السنوي:</span>
                  <span className="font-medium text-blue-600">{policy.premium.toLocaleString()} ر.س</span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">مبلغ التغطية:</span>
                  <span className="font-medium text-green-600">{policy.coverage.toLocaleString()} ر.س</span>
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">تاريخ الانتهاء:</span>
                  <span className={`font-medium ${daysUntilExpiry <= 30 ? 'text-red-600' : 'text-gray-900'}`}>
                    {policy.endDate}
                  </span>
                </div>
                
                {daysUntilExpiry <= 60 && daysUntilExpiry > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 mt-4">
                    <div className="flex items-center gap-2">
                      <ExclamationTriangleIcon className="w-4 h-4 text-yellow-600" />
                      <span className="text-sm text-yellow-800">
                        ينتهي خلال {daysUntilExpiry} يوم
                      </span>
                    </div>
                  </div>
                )}
              </div>

              <div className="mt-4 pt-4 border-t flex gap-2">
                <button className="flex-1 text-sm bg-gray-100 text-gray-700 py-2 px-3 rounded hover:bg-gray-200">
                  عرض التفاصيل
                </button>
                <button className="flex-1 text-sm bg-blue-600 text-white py-2 px-3 rounded hover:bg-blue-700">
                  تجديد
                </button>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold mb-4">حاسبة التأمين</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع التأمين</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                <option>تأمين صحي</option>
                <option>تأمين السيارة</option>
                <option>تأمين المنزل</option>
                <option>تأمين على الحياة</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">العمر</label>
              <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="25" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مبلغ التغطية المطلوب</label>
              <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="100000" />
            </div>
            <button className="w-full btn-primary">
              احسب القسط
            </button>
          </div>
        </div>

        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">🛡️ نصائح التأمين</h3>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">راجع بوالصك بانتظام وحدثها عند الحاجة</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">احتفظ بنسخ من جميع وثائق التأمين</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">قارن الأسعار من عدة شركات</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">اقرأ الشروط والأحكام بعناية</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">جدد بوالصك قبل انتهائها</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}