'use client'

import { useState, useEffect } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import AutoSaveIndicator from '@/components/AutoSaveIndicator'
import {
  UserIcon,
  LockClosedIcon,
  BellIcon,
  ShieldCheckIcon,
  DevicePhoneMobileIcon,
  EnvelopeIcon,
  EyeIcon,
  EyeSlashIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

export default function SettingsPage() {
  const { userData, updateProfile, updateSettings, isLoading } = useData()
  const { success, error } = useToastContext()
  const [activeTab, setActiveTab] = useState('profile')
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  
  // Form states
  const [profileForm, setProfileForm] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    birthDate: '',
    address: ''
  })
  
  const [notifications, setNotifications] = useState({
    email: true,
    sms: true,
    push: false,
    weeklyReport: true,
    monthlyReport: true,
    paymentReminders: true,
    securityAlerts: true
  })

  // تحميل البيانات عند بدء التحميل
  useEffect(() => {
    if (!isLoading && userData) {
      setProfileForm({
        firstName: userData.profile.firstName || '',
        lastName: userData.profile.lastName || '',
        email: userData.profile.email || '',
        phone: userData.profile.phone || '',
        birthDate: userData.profile.birthDate || '',
        address: userData.profile.address || ''
      })
      
      if (userData.settings?.notifications) {
        setNotifications(userData.settings.notifications)
      }
    }
  }, [isLoading, userData])

  const tabs = [
    { id: 'profile', name: 'المعلومات الشخصية', icon: UserIcon },
    { id: 'security', name: 'الأمان', icon: ShieldCheckIcon },
    { id: 'notifications', name: 'الإشعارات', icon: BellIcon },
    { id: 'privacy', name: 'الخصوصية', icon: LockClosedIcon }
  ]

  const handleNotificationChange = (key: string, value: boolean) => {
    const updatedNotifications = {
      ...notifications,
      [key]: value
    }
    setNotifications(updatedNotifications)
    
    // حفظ فوري للإعدادات
    updateSettings({
      notifications: updatedNotifications
    })
    
    setLastSaved(new Date())
    success('تم حفظ إعدادات الإشعارات ✅')
  }

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSaving(true)
    
    try {
      updateProfile(profileForm)
      success('تم حفظ المعلومات الشخصية بنجاح! ✅')
    } catch (err) {
      console.error('Error saving profile:', err)
      error('حدث خطأ في حفظ المعلومات الشخصية')
    } finally {
      setIsSaving(false)
    }
  }

  const handleProfileInputChange = (field: string, value: string) => {
    const updatedForm = {
      ...profileForm,
      [field]: value
    }
    setProfileForm(updatedForm)
    
    // حفظ فوري (بدون تأخير)
    updateProfile({ [field]: value })
    setLastSaved(new Date())
    success(`تم حفظ ${getFieldLabel(field)} تلقائياً ✅`)
  }

  const getFieldLabel = (field: string) => {
    const labels: { [key: string]: string } = {
      firstName: 'الاسم الأول',
      lastName: 'اسم العائلة', 
      email: 'البريد الإلكتروني',
      phone: 'رقم الهاتف',
      birthDate: 'تاريخ الميلاد',
      address: 'العنوان'
    }
    return labels[field] || field
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">إعدادات الحساب</h1>
        <p className="mt-1 text-sm text-gray-600">
          إدارة معلوماتك الشخصية وإعدادات الأمان
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Sidebar */}
        <div className="lg:col-span-1">
          <nav className="space-y-1">
            {tabs.map((tab) => {
              const IconComponent = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`w-full flex items-center space-x-3 space-x-reverse px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    activeTab === tab.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <IconComponent className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              )
            })}
          </nav>
        </div>

        {/* Content */}
        <div className="lg:col-span-3">
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">المعلومات الشخصية</h2>
                
                {/* Profile Picture */}
                <div className="flex items-center space-x-6 space-x-reverse mb-8">
                  <div className="w-20 h-20 bg-gray-200 rounded-full flex items-center justify-center">
                    <UserIcon className="w-10 h-10 text-gray-500" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-gray-900">صورة الملف الشخصي</h3>
                    <p className="text-sm text-gray-500">JPG أو PNG، حد أقصى 2MB</p>
                    <div className="flex space-x-2 space-x-reverse mt-2">
                      <button 
                        onClick={() => {
                          const input = document.createElement('input')
                          input.type = 'file'
                          input.accept = 'image/*'
                          input.onchange = (e) => {
                            const file = (e.target as HTMLInputElement).files?.[0]
                            if (file) {
                              alert('تم اختيار الصورة: ' + file.name)
                            }
                          }
                          input.click()
                        }}
                        className="btn-primary text-sm px-3 py-1"
                      >
                        رفع صورة
                      </button>
                      <button 
                        onClick={() => alert('تم حذف الصورة')}
                        className="btn-secondary text-sm px-3 py-1"
                      >
                        حذف
                      </button>
                    </div>
                  </div>
                </div>

                <form onSubmit={handleProfileSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        الاسم الأول
                      </label>
                      <input
                        type="text"
                        value={profileForm.firstName}
                        onChange={(e) => handleProfileInputChange('firstName', e.target.value)}
                        className="input-field"
                        required
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        اسم العائلة
                      </label>
                      <input
                        type="text"
                        value={profileForm.lastName}
                        onChange={(e) => handleProfileInputChange('lastName', e.target.value)}
                        className="input-field"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      البريد الإلكتروني
                    </label>
                    <input
                      type="email"
                      value={profileForm.email}
                      onChange={(e) => handleProfileInputChange('email', e.target.value)}
                      className="input-field"
                      required
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        رقم الهاتف
                      </label>
                      <input
                        type="tel"
                        value={profileForm.phone}
                        onChange={(e) => handleProfileInputChange('phone', e.target.value)}
                        className="input-field"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تاريخ الميلاد
                      </label>
                      <input
                        type="date"
                        value={profileForm.birthDate}
                        onChange={(e) => handleProfileInputChange('birthDate', e.target.value)}
                        className="input-field"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      العنوان
                    </label>
                    <textarea
                      rows={3}
                      value={profileForm.address}
                      onChange={(e) => handleProfileInputChange('address', e.target.value)}
                      className="input-field"
                    />
                  </div>

                  <div className="flex justify-end">
                    <button 
                      type="submit" 
                      disabled={isSaving}
                      className={`btn-primary ${isSaving ? 'opacity-50 cursor-not-allowed' : ''}`}
                    >
                      {isSaving ? 'جاري الحفظ...' : 'حفظ التغييرات'}
                    </button>
                  </div>
                </form>
              </div>
            )}

            {/* Security Tab */}
            {activeTab === 'security' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">إعدادات الأمان</h2>

                {/* Password Change */}
                <div className="mb-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">تغيير كلمة المرور</h3>
                  <form className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور الحالية
                      </label>
                      <div className="relative">
                        <input
                          type={showCurrentPassword ? 'text' : 'password'}
                          className="input-field pl-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showCurrentPassword ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور الجديدة
                      </label>
                      <div className="relative">
                        <input
                          type={showNewPassword ? 'text' : 'password'}
                          className="input-field pl-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showNewPassword ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تأكيد كلمة المرور الجديدة
                      </label>
                      <div className="relative">
                        <input
                          type={showConfirmPassword ? 'text' : 'password'}
                          className="input-field pl-10"
                        />
                        <button
                          type="button"
                          onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2"
                        >
                          {showConfirmPassword ? (
                            <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                          ) : (
                            <EyeIcon className="h-5 w-5 text-gray-400" />
                          )}
                        </button>
                      </div>
                    </div>

                    <button 
                      type="submit" 
                      onClick={(e) => {
                        e.preventDefault()
                        alert('تم تحديث كلمة المرور بنجاح!')
                      }}
                      className="btn-primary"
                    >
                      تحديث كلمة المرور
                    </button>
                  </form>
                </div>

                {/* Two-Factor Authentication */}
                <div className="border-t border-gray-200 pt-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">المصادقة الثنائية</h3>
                  <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                        <ShieldCheckIcon className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">المصادقة عبر الرسائل النصية</h4>
                        <p className="text-sm text-gray-600">حماية إضافية لحسابك</p>
                      </div>
                    </div>
                    <div className="flex items-center">
                      <span className="text-sm text-green-600 ml-2">مفعل</span>
                      <button className="btn-secondary text-sm">إعدادات</button>
                    </div>
                  </div>
                </div>

                {/* Login Sessions */}
                <div className="border-t border-gray-200 pt-8 mt-8">
                  <h3 className="text-md font-medium text-gray-900 mb-4">جلسات تسجيل الدخول</h3>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <DevicePhoneMobileIcon className="w-5 h-5 text-gray-600" />
                        <div>
                          <h4 className="font-medium text-gray-900">iPhone 13</h4>
                          <p className="text-sm text-gray-600">آخر نشاط: الآن</p>
                        </div>
                      </div>
                      <span className="text-sm text-green-600">الجلسة الحالية</span>
                    </div>
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <DevicePhoneMobileIcon className="w-5 h-5 text-gray-600" />
                        <div>
                          <h4 className="font-medium text-gray-900">Chrome - Windows</h4>
                          <p className="text-sm text-gray-600">آخر نشاط: منذ 3 ساعات</p>
                        </div>
                      </div>
                      <button className="text-red-600 hover:text-red-700 text-sm">
                        إنهاء الجلسة
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notifications Tab */}
            {activeTab === 'notifications' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">إعدادات الإشعارات</h2>

                <div className="space-y-6">
                  {/* Email Notifications */}
                  <div>
                    <h3 className="text-md font-medium text-gray-900 mb-4">إشعارات البريد الإلكتروني</h3>
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <EnvelopeIcon className="w-5 h-5 text-gray-600" />
                          <div>
                            <h4 className="font-medium text-gray-900">التقارير الأسبوعية</h4>
                            <p className="text-sm text-gray-600">ملخص أسبوعي لنشاطك المالي</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.weeklyReport}
                            onChange={(e) => handleNotificationChange('weeklyReport', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <EnvelopeIcon className="w-5 h-5 text-gray-600" />
                          <div>
                            <h4 className="font-medium text-gray-900">التقارير الشهرية</h4>
                            <p className="text-sm text-gray-600">تقرير شامل عن الوضع المالي الشهري</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.monthlyReport}
                            onChange={(e) => handleNotificationChange('monthlyReport', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <EnvelopeIcon className="w-5 h-5 text-gray-600" />
                          <div>
                            <h4 className="font-medium text-gray-900">تذكير بالمدفوعات</h4>
                            <p className="text-sm text-gray-600">تذكير قبل استحقاق الفواتير</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.paymentReminders}
                            onChange={(e) => handleNotificationChange('paymentReminders', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3 space-x-reverse">
                          <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
                          <div>
                            <h4 className="font-medium text-gray-900">تنبيهات الأمان</h4>
                            <p className="text-sm text-gray-600">إشعارات عند تسجيل دخول جديد</p>
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={notifications.securityAlerts}
                            onChange={(e) => handleNotificationChange('securityAlerts', e.target.checked)}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>

                  {/* SMS Notifications */}
                  <div className="border-t border-gray-200 pt-6">
                    <h3 className="text-md font-medium text-gray-900 mb-4">إشعارات الرسائل النصية</h3>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3 space-x-reverse">
                        <DevicePhoneMobileIcon className="w-5 h-5 text-gray-600" />
                        <div>
                          <h4 className="font-medium text-gray-900">تفعيل الرسائل النصية</h4>
                          <p className="text-sm text-gray-600">إشعارات مهمة عبر SMS</p>
                        </div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={notifications.sms}
                          onChange={(e) => handleNotificationChange('sms', e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-primary-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-primary-600"></div>
                      </label>
                    </div>
                  </div>
                </div>

                <div className="mt-8 flex justify-end">
                  <button className="btn-primary">
                    حفظ الإعدادات
                  </button>
                </div>
              </div>
            )}

            {/* Privacy Tab */}
            {activeTab === 'privacy' && (
              <div className="p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-6">إعدادات الخصوصية</h2>

                <div className="space-y-6">
                  <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <CheckCircleIcon className="w-5 h-5 text-green-600" />
                      <h3 className="font-medium text-green-800">حسابك محمي</h3>
                    </div>
                    <p className="text-sm text-green-700 mt-2">
                      جميع بياناتك محمية بأحدث معايير الأمان والتشفير
                    </p>
                  </div>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">تشفير البيانات</h4>
                        <p className="text-sm text-gray-600">تشفير جميع البيانات المالية الحساسة</p>
                      </div>
                      <CheckCircleIcon className="w-5 h-5 text-green-600" />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">مراقبة الأنشطة المشبوهة</h4>
                        <p className="text-sm text-gray-600">رصد تلقائي للأنشطة غير المعتادة</p>
                      </div>
                      <CheckCircleIcon className="w-5 h-5 text-green-600" />
                    </div>

                    <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                      <div>
                        <h4 className="font-medium text-gray-900">النسخ الاحتياطي التلقائي</h4>
                        <p className="text-sm text-gray-600">حفظ تلقائي آمن لجميع بياناتك</p>
                      </div>
                      <CheckCircleIcon className="w-5 h-5 text-green-600" />
                    </div>
                  </div>

                  <div className="border-t border-gray-200 pt-6">
                    <h3 className="text-md font-medium text-gray-900 mb-4">إدارة البيانات</h3>
                    <div className="space-y-3">
                      <button 
                        onClick={() => {
                          const dataStr = JSON.stringify({
                            profile: 'بيانات المستخدم',
                            transactions: 'المعاملات المالية',
                            documents: 'المستندات'
                          }, null, 2)
                          const dataBlob = new Blob([dataStr], {type: 'application/json'})
                          const url = URL.createObjectURL(dataBlob)
                          const link = document.createElement('a')
                          link.href = url
                          link.download = 'my-financial-data.json'
                          link.click()
                          URL.revokeObjectURL(url)
                        }}
                        className="w-full text-right p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                      >
                        تحميل نسخة من بياناتي
                      </button>
                      <button 
                        onClick={() => {
                          if (confirm('هل أنت متأكد من حذف الحساب نهائياً؟ هذه العملية لا يمكن التراجع عنها.')) {
                            alert('تم إرسال طلب حذف الحساب. سيتم التواصل معك قريباً.')
                          }
                        }}
                        className="w-full text-right p-3 border border-red-200 rounded-lg hover:bg-red-50 text-red-700 transition-colors"
                      >
                        حذف الحساب نهائياً
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}