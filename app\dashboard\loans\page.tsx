'use client'

import { useState } from 'react'
import {
  BanknotesIcon,
  CalculatorIcon,
  CalendarIcon,
  ArrowTrendingDownIcon as TrendingDownIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  PlusIcon,
  HomeIcon,
  TruckIcon,
  AcademicCapIcon
} from '@heroicons/react/24/outline'

export default function LoansPage() {
  const [loans] = useState([
    {
      id: '1',
      name: 'قرض السيارة',
      type: 'شخصي',
      bank: 'البنك الأهلي السعودي',
      originalAmount: 80000,
      remainingAmount: 45000,
      monthlyPayment: 1200,
      interestRate: 4.5,
      startDate: '2023-01-15',
      endDate: '2027-01-15',
      status: 'active',
      icon: TruckIcon,
      color: 'blue'
    },
    {
      id: '2',
      name: 'قرض عقاري',
      type: 'عقاري',
      bank: 'بنك الراجحي',
      originalAmount: 500000,
      remainingAmount: 420000,
      monthlyPayment: 3500,
      interestRate: 3.8,
      startDate: '2022-06-01',
      endDate: '2037-06-01',
      status: 'active',
      icon: HomeIcon,
      color: 'green'
    },
    {
      id: '3',
      name: 'قرض تعليمي',
      type: 'تعليمي',
      bank: 'بنك سامبا',
      originalAmount: 50000,
      remainingAmount: 15000,
      monthlyPayment: 800,
      interestRate: 2.5,
      startDate: '2021-09-01',
      endDate: '2025-09-01',
      status: 'active',
      icon: AcademicCapIcon,
      color: 'purple'
    }
  ])

  const totalOriginal = loans.reduce((sum, loan) => sum + loan.originalAmount, 0)
  const totalRemaining = loans.reduce((sum, loan) => sum + loan.remainingAmount, 0)
  const totalMonthlyPayments = loans.reduce((sum, loan) => sum + loan.monthlyPayment, 0)
  const totalPaid = totalOriginal - totalRemaining

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'border-blue-200 bg-blue-50',
      green: 'border-green-200 bg-green-50',
      purple: 'border-purple-200 bg-purple-50',
      red: 'border-red-200 bg-red-50'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  const calculateMonthsRemaining = (endDate: string) => {
    const today = new Date()
    const end = new Date(endDate)
    const diffTime = end.getTime() - today.getTime()
    const diffMonths = Math.ceil(diffTime / (1000 * 60 * 60 * 24 * 30))
    return Math.max(0, diffMonths)
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة القروض</h1>
          <p className="text-gray-600">تتبع وإدارة قروضك والتزاماتك المالية</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          قرض جديد
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي القروض</p>
              <p className="text-2xl font-bold text-gray-900">{totalOriginal.toLocaleString()} ر.س</p>
            </div>
            <BanknotesIcon className="w-8 h-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المدفوع</p>
              <p className="text-2xl font-bold text-green-600">{totalPaid.toLocaleString()} ر.س</p>
            </div>
            <CheckCircleIcon className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المتبقي</p>
              <p className="text-2xl font-bold text-red-600">{totalRemaining.toLocaleString()} ر.س</p>
            </div>
            <TrendingDownIcon className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الأقساط الشهرية</p>
              <p className="text-2xl font-bold text-blue-600">{totalMonthlyPayments.toLocaleString()} ر.س</p>
            </div>
            <CalendarIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {loans.map((loan) => {
          const IconComponent = loan.icon
          const paidPercentage = ((loan.originalAmount - loan.remainingAmount) / loan.originalAmount) * 100
          const monthsRemaining = calculateMonthsRemaining(loan.endDate)
          
          return (
            <div key={loan.id} className={`bg-white rounded-lg shadow-sm border-2 ${getColorClasses(loan.color)} p-6`}>
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(loan.color)}`}>
                    <IconComponent className="w-6 h-6 text-gray-700" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{loan.name}</h3>
                    <p className="text-sm text-gray-500">{loan.type} - {loan.bank}</p>
                  </div>
                </div>
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium text-green-600 bg-green-50">
                  <CheckCircleIcon className="w-3 h-3 ml-1" />
                  نشط
                </span>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">المبلغ الأصلي:</span>
                    <p className="font-medium text-gray-900">{loan.originalAmount.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <span className="text-gray-600">المتبقي:</span>
                    <p className="font-medium text-red-600">{loan.remainingAmount.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <span className="text-gray-600">القسط الشهري:</span>
                    <p className="font-medium text-blue-600">{loan.monthlyPayment.toLocaleString()} ر.س</p>
                  </div>
                  <div>
                    <span className="text-gray-600">معدل الفائدة:</span>
                    <p className="font-medium text-purple-600">{loan.interestRate}%</p>
                  </div>
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>التقدم في السداد</span>
                    <span className="font-medium">{paidPercentage.toFixed(1)}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="h-3 bg-green-500 rounded-full transition-all duration-300"
                      style={{ width: `${paidPercentage}%` }}
                    ></div>
                  </div>
                </div>

                <div className="flex justify-between items-center text-sm pt-2 border-t">
                  <span className="text-gray-600">
                    متبقي: {monthsRemaining} شهر
                  </span>
                  <span className="text-gray-600">
                    انتهاء: {loan.endDate}
                  </span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t flex gap-2">
                <button className="flex-1 text-sm bg-gray-100 text-gray-700 py-2 px-3 rounded hover:bg-gray-200">
                  عرض التفاصيل
                </button>
                <button className="flex-1 text-sm bg-blue-600 text-white py-2 px-3 rounded hover:bg-blue-700">
                  دفع إضافي
                </button>
              </div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold mb-4">حاسبة القروض</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مبلغ القرض</label>
              <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="100000" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">معدل الفائدة السنوي (%)</label>
              <input type="number" step="0.1" className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="4.5" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">مدة القرض (سنوات)</label>
              <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="5" />
            </div>
            <button className="w-full btn-primary">
              احسب القسط الشهري
            </button>
            <div className="bg-gray-50 p-4 rounded-lg">
              <p className="text-sm text-gray-600">القسط الشهري المتوقع:</p>
              <p className="text-xl font-bold text-blue-600">-- ر.س</p>
            </div>
          </div>
        </div>

        <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-6 border border-orange-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">💰 نصائح إدارة القروض</h3>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">ادفع أكثر من الحد الأدنى عند الإمكان</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">أعد تمويل القروض عالية الفائدة</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">ركز على سداد القروض عالية الفائدة أولاً</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">تجنب الاقتراض لأغراض استهلاكية</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-orange-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">احتفظ بسجل دقيق لجميع المدفوعات</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-yellow-50 rounded-lg p-6 border border-yellow-200">
        <div className="flex items-start gap-4">
          <ExclamationTriangleIcon className="w-6 h-6 text-yellow-600 mt-1" />
          <div>
            <h3 className="font-semibold text-yellow-900 mb-2">تذكير مهم</h3>
            <p className="text-sm text-yellow-800">
              تأكد من قدرتك على سداد الأقساط الشهرية قبل الحصول على أي قرض جديد. 
              نسبة الدين إلى الدخل يجب ألا تتجاوز 33% من دخلك الشهري.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}