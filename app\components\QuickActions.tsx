'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import { useNotifications } from '@/contexts/NotificationContext'
import {
  PlusIcon,
  CurrencyDollarIcon,
  DocumentArrowUpIcon,
  CalculatorIcon,
  ChartBarIcon,
  BoltIcon,
  ClockIcon,
  StarIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface QuickAction {
  id: string
  title: string
  description: string
  icon: React.ComponentType<any>
  color: string
  bgColor: string
  action: () => void
  shortcut?: string
}

export default function QuickActions() {
  const { addTransaction } = useData()
  const { success } = useToastContext()
  const { addNotification } = useNotifications()
  const [showCalculator, setShowCalculator] = useState(false)
  const [calcResult, setCalcResult] = useState('')

  const quickActions: QuickAction[] = [
    {
      id: 'add-income',
      title: 'إضافة دخل سريع',
      description: 'راتب أو دخل إضافي',
      icon: CurrencyDollarIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50 hover:bg-green-100',
      shortcut: 'Ctrl+I',
      action: () => {
        const amount = prompt('أدخل مبلغ الدخل:')
        if (amount && !isNaN(Number(amount))) {
          addTransaction({
            title: 'دخل سريع',
            amount: Number(amount),
            category: 'دخل',
            date: new Date().toISOString().split('T')[0],
            status: 'completed',
            type: 'income',
            time: new Date().toLocaleTimeString('ar-SA'),
            description: 'دخل سريع',
            paymentMethod: 'cash',
            reference: ''
          })
          success(`تم إضافة دخل بقيمة ${amount} ر.س`)
        }
      }
    },
    {
      id: 'add-expense',
      title: 'إضافة مصروف سريع',
      description: 'مصروف يومي أو طارئ',
      icon: CurrencyDollarIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-50 hover:bg-red-100',
      shortcut: 'Ctrl+E',
      action: () => {
        const amount = prompt('أدخل مبلغ المصروف:')
        if (amount && !isNaN(Number(amount))) {
          addTransaction({
            title: 'مصروف سريع',
            amount: -Number(amount),
            category: 'مصروفات',
            date: new Date().toISOString().split('T')[0],
            status: 'completed',
            type: 'payment',
            time: new Date().toLocaleTimeString('ar-SA'),
            description: 'مصروف سريع',
            paymentMethod: 'cash',
            reference: ''
          })
          success(`تم إضافة مصروف بقيمة ${amount} ر.س`)
        }
      }
    },
    {
      id: 'calculator',
      title: 'حاسبة سريعة',
      description: 'حسابات مالية فورية',
      icon: CalculatorIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50 hover:bg-blue-100',
      shortcut: 'Ctrl+C',
      action: () => setShowCalculator(true)
    },
    {
      id: 'reminder',
      title: 'تذكير سريع',
      description: 'إضافة تذكير مالي',
      icon: ClockIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50 hover:bg-purple-100',
      shortcut: 'Ctrl+R',
      action: () => {
        const reminder = prompt('أدخل نص التذكير:')
        if (reminder) {
          addNotification({
            title: 'تذكير مالي',
            message: reminder,
            type: 'info',
            priority: 'medium'
          })
          success('تم إضافة التذكير')
        }
      }
    }
  ]

  // حاسبة بسيطة
  const SimpleCalculator = () => {
    const [display, setDisplay] = useState('0')
    const [previousValue, setPreviousValue] = useState<number | null>(null)
    const [operation, setOperation] = useState<string | null>(null)
    const [waitingForOperand, setWaitingForOperand] = useState(false)

    const inputNumber = (num: string) => {
      if (waitingForOperand) {
        setDisplay(num)
        setWaitingForOperand(false)
      } else {
        setDisplay(display === '0' ? num : display + num)
      }
    }

    const inputOperation = (nextOperation: string) => {
      const inputValue = parseFloat(display)

      if (previousValue === null) {
        setPreviousValue(inputValue)
      } else if (operation) {
        const currentValue = previousValue || 0
        const newValue = calculate(currentValue, inputValue, operation)

        setDisplay(String(newValue))
        setPreviousValue(newValue)
      }

      setWaitingForOperand(true)
      setOperation(nextOperation)
    }

    const calculate = (firstValue: number, secondValue: number, operation: string) => {
      switch (operation) {
        case '+':
          return firstValue + secondValue
        case '-':
          return firstValue - secondValue
        case '×':
          return firstValue * secondValue
        case '÷':
          return firstValue / secondValue
        default:
          return secondValue
      }
    }

    const performCalculation = () => {
      const inputValue = parseFloat(display)

      if (previousValue !== null && operation) {
        const newValue = calculate(previousValue, inputValue, operation)
        setDisplay(String(newValue))
        setCalcResult(String(newValue))
        setPreviousValue(null)
        setOperation(null)
        setWaitingForOperand(true)
      }
    }

    const clear = () => {
      setDisplay('0')
      setPreviousValue(null)
      setOperation(null)
      setWaitingForOperand(false)
    }

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 w-80">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold">حاسبة سريعة</h3>
            <button
              onClick={() => setShowCalculator(false)}
              className="text-gray-500 hover:text-gray-700"
            >
              ×
            </button>
          </div>
          
          <div className="bg-gray-100 p-4 rounded-lg mb-4 text-right">
            <div className="text-2xl font-mono">{display}</div>
          </div>

          <div className="grid grid-cols-4 gap-2">
            <button onClick={clear} className="col-span-2 bg-red-500 text-white p-3 rounded hover:bg-red-600">
              مسح
            </button>
            <button onClick={() => inputOperation('÷')} className="bg-gray-300 p-3 rounded hover:bg-gray-400">
              ÷
            </button>
            <button onClick={() => inputOperation('×')} className="bg-gray-300 p-3 rounded hover:bg-gray-400">
              ×
            </button>
            
            {[7, 8, 9].map(num => (
              <button key={num} onClick={() => inputNumber(String(num))} className="bg-gray-200 p-3 rounded hover:bg-gray-300">
                {num}
              </button>
            ))}
            <button onClick={() => inputOperation('-')} className="bg-gray-300 p-3 rounded hover:bg-gray-400">
              -
            </button>
            
            {[4, 5, 6].map(num => (
              <button key={num} onClick={() => inputNumber(String(num))} className="bg-gray-200 p-3 rounded hover:bg-gray-300">
                {num}
              </button>
            ))}
            <button onClick={() => inputOperation('+')} className="bg-gray-300 p-3 rounded hover:bg-gray-400">
              +
            </button>
            
            {[1, 2, 3].map(num => (
              <button key={num} onClick={() => inputNumber(String(num))} className="bg-gray-200 p-3 rounded hover:bg-gray-300">
                {num}
              </button>
            ))}
            <button onClick={performCalculation} className="row-span-2 bg-primary-500 text-white p-3 rounded hover:bg-primary-600">
              =
            </button>
            
            <button onClick={() => inputNumber('0')} className="col-span-2 bg-gray-200 p-3 rounded hover:bg-gray-300">
              0
            </button>
            <button onClick={() => inputNumber('.')} className="bg-gray-200 p-3 rounded hover:bg-gray-300">
              .
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <>
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <div className="flex items-center space-x-2 space-x-reverse mb-4">
          <BoltIcon className="w-5 h-5 text-yellow-500" />
          <h2 className="text-lg font-semibold text-gray-900">إجراءات سريعة</h2>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {quickActions.map((action) => {
            const IconComponent = action.icon
            return (
              <button
                key={action.id}
                onClick={action.action}
                className={`${action.bgColor} p-4 rounded-lg border border-gray-200 transition-all duration-200 hover:shadow-md group`}
              >
                <div className="flex items-center space-x-3 space-x-reverse">
                  <div className={`w-10 h-10 ${action.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}>
                    <IconComponent className={`w-5 h-5 ${action.color}`} />
                  </div>
                  <div className="flex-1 text-right">
                    <h3 className="font-medium text-gray-900">{action.title}</h3>
                    <p className="text-sm text-gray-600">{action.description}</p>
                    {action.shortcut && (
                      <p className="text-xs text-gray-400 mt-1">{action.shortcut}</p>
                    )}
                  </div>
                </div>
              </button>
            )
          })}
        </div>

        {/* اختصارات إضافية */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <h3 className="text-sm font-medium text-gray-700 mb-3">روابط سريعة</h3>
          <div className="flex flex-wrap gap-2">
            <Link href="/dashboard/reports" className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors">
              <ChartBarIcon className="w-4 h-4 ml-1" />
              التقارير
            </Link>
            <Link href="/dashboard/documents" className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors">
              <DocumentArrowUpIcon className="w-4 h-4 ml-1" />
              المستندات
            </Link>
            <Link href="/dashboard/settings" className="inline-flex items-center px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors">
              <StarIcon className="w-4 h-4 ml-1" />
              الإعدادات
            </Link>
          </div>
        </div>
      </div>

      {showCalculator && <SimpleCalculator />}
    </>
  )
}