'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import {
  DocumentTextIcon,
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  FunnelIcon,
  MagnifyingGlassIcon,
  CloudArrowUpIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import InvoiceTemplates from '@/components/InvoiceTemplates'

const statusConfig = {
  paid: { label: 'مدفوع', color: 'text-green-600 bg-green-50', icon: CheckCircleIcon },
  pending: { label: 'معلق', color: 'text-yellow-600 bg-yellow-50', icon: ExclamationTriangleIcon },
  overdue: { label: 'متأخر', color: 'text-red-600 bg-red-50', icon: XCircleIcon },
  draft: { label: 'مسودة', color: 'text-gray-600 bg-gray-50', icon: DocumentTextIcon }
}

export default function InvoicesPage() {
  const { userData, addInvoice, deleteInvoice } = useData()
  const { success } = useToastContext()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [showViewModal, setShowViewModal] = useState(false)
  const [showEditModal, setShowEditModal] = useState(false)
  const [selectedInvoice, setSelectedInvoice] = useState<any>(null)
  const [editForm, setEditForm] = useState({
    title: '',
    company: '',
    amount: 0,
    dueDate: '',
    status: 'pending',
    category: '',
    description: ''
  })

  const invoices = userData?.invoices || []

  const handleDeleteInvoice = (id: string, title: string) => {
    if (confirm(`هل أنت متأكد من حذف فاتورة "${title}"؟`)) {
      deleteInvoice(id)
      success('تم حذف الفاتورة بنجاح')
    }
  }

  const handleAddInvoice = () => {
    const newInvoice = {
      title: 'فاتورة جديدة',
      company: 'شركة تجريبية',
      amount: 100,
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      status: 'pending' as const,
      category: 'عام',
      description: 'فاتورة تجريبية',
      attachments: [],
      createdAt: new Date().toISOString()
    }
    
    addInvoice(newInvoice)
    success('تم إضافة فاتورة جديدة بنجاح! ✅')
  }

  const handleViewInvoice = (invoice: any) => {
    setSelectedInvoice(invoice)
    setShowViewModal(true)
  }

  const handleEditInvoice = (invoice: any) => {
    setSelectedInvoice(invoice)
    setEditForm({
      title: invoice.title,
      company: invoice.company,
      amount: invoice.amount,
      dueDate: invoice.dueDate,
      status: invoice.status,
      category: invoice.category,
      description: invoice.description
    })
    setShowEditModal(true)
  }

  const handleSaveEdit = () => {
    if (selectedInvoice) {
      success('تم حفظ التعديلات بنجاح! ✅')
      setShowEditModal(false)
    }
  }

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (invoice.company || '').toLowerCase().includes(searchTerm.toLowerCase())
    const matchesStatus = selectedStatus === 'all' || invoice.status === selectedStatus
    return matchesSearch && matchesStatus
  })

  const getStatusStats = () => {
    return {
      total: invoices.length,
      paid: invoices.filter(inv => inv.status === 'paid').length,
      pending: invoices.filter(inv => inv.status === 'pending').length,
      overdue: invoices.filter(inv => inv.status === 'overdue').length,
      totalAmount: invoices.reduce((sum, inv) => sum + inv.amount, 0)
    }
  }

  const stats = getStatusStats()

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الفواتير</h1>
          <p className="mt-1 text-sm text-gray-600">إدارة وتتبع جميع فواتيرك المالية</p>
        </div>
        <button onClick={handleAddInvoice} className="mt-4 sm:mt-0 btn-primary flex items-center space-x-2 space-x-reverse">
          <PlusIcon className="w-5 h-5" />
          <span>إضافة فاتورة جديدة</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الفواتير</p>
              <p className="text-xl font-bold text-gray-900">{stats.total}</p>
            </div>
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <DocumentTextIcon className="w-5 h-5 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">مدفوع</p>
              <p className="text-xl font-bold text-green-600">{stats.paid}</p>
            </div>
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <CheckCircleIcon className="w-5 h-5 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">معلق</p>
              <p className="text-xl font-bold text-yellow-600">{stats.pending}</p>
            </div>
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm p-4 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">متأخر</p>
              <p className="text-xl font-bold text-red-600">{stats.overdue}</p>
            </div>
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <XCircleIcon className="w-5 h-5 text-red-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="البحث في الفواتير..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full sm:w-64 pl-3 pr-10 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              />
            </div>
            <div className="flex items-center gap-2">
              <FunnelIcon className="w-4 h-4 text-gray-400" />
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              >
                <option value="all">جميع الحالات</option>
                <option value="paid">مدفوع</option>
                <option value="pending">معلق</option>
                <option value="overdue">متأخر</option>
                <option value="draft">مسودة</option>
              </select>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            إجمالي القيمة: <span className="font-semibold text-primary-600">{stats.totalAmount.toLocaleString()} ر.س</span>
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        {filteredInvoices.length === 0 ? (
          <div className="text-center py-12">
            <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد فواتير</h3>
            <p className="mt-1 text-sm text-gray-500">
              {searchTerm || selectedStatus !== 'all' 
                ? 'لم يتم العثور على نتائج مطابقة لبحثك'
                : 'ابدأ بإضافة فاتورة جديدة'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredInvoices.map((invoice) => {
              const StatusIcon = statusConfig[invoice.status as keyof typeof statusConfig].icon
              return (
                <div key={invoice.id} className="p-4 hover:bg-gray-50 transition-colors">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                        <DocumentTextIcon className="w-5 h-5 text-gray-600" />
                      </div>
                      <div className="flex-1">
                        <h3 className="text-sm font-medium text-gray-900">{invoice.title}</h3>
                        <p className="text-sm text-gray-600">{invoice.company}</p>
                        <div className="flex items-center space-x-4 space-x-reverse text-xs text-gray-500 mt-1">
                          <span>{invoice.amount.toLocaleString()} ر.س</span>
                          <span>الاستحقاق: {invoice.dueDate}</span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 space-x-reverse">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusConfig[invoice.status as keyof typeof statusConfig].color}`}>
                        <StatusIcon className="w-3 h-3 ml-1" />
                        {statusConfig[invoice.status as keyof typeof statusConfig].label}
                      </span>
                      <div className="flex items-center space-x-1 space-x-reverse">
                        <button 
                          onClick={() => handleViewInvoice(invoice)}
                          className="text-primary-600 hover:text-primary-700 p-1.5 rounded hover:bg-primary-50"
                          title="عرض التفاصيل"
                        >
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleEditInvoice(invoice)}
                          className="text-gray-600 hover:text-gray-700 p-1.5 rounded hover:bg-gray-50"
                          title="تعديل"
                        >
                          <PencilIcon className="w-4 h-4" />
                        </button>
                        <button 
                          onClick={() => handleDeleteInvoice(invoice.id, invoice.title)}
                          className="text-red-600 hover:text-red-700 p-1.5 rounded hover:bg-red-50"
                          title="حذف"
                        >
                          <TrashIcon className="w-4 h-4" />
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        )}
      </div>

      <InvoiceTemplates />

      {showViewModal && selectedInvoice && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setShowViewModal(false)}></div>
            <div className="relative bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">تفاصيل الفاتورة</h3>
              <div className="space-y-3 text-sm">
                <div><strong>العنوان:</strong> {selectedInvoice.title}</div>
                <div><strong>الشركة:</strong> {selectedInvoice.company}</div>
                <div><strong>المبلغ:</strong> {selectedInvoice.amount.toLocaleString()} ر.س</div>
                <div><strong>تاريخ الاستحقاق:</strong> {selectedInvoice.dueDate}</div>
                <div><strong>الحالة:</strong> {statusConfig[selectedInvoice.status as keyof typeof statusConfig].label}</div>
              </div>
              <div className="mt-6">
                <button onClick={() => setShowViewModal(false)} className="w-full px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                  إغلاق
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {showEditModal && selectedInvoice && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen px-4">
            <div className="fixed inset-0 bg-gray-500 bg-opacity-75" onClick={() => setShowEditModal(false)}></div>
            <div className="relative bg-white rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium text-gray-900 mb-4">تعديل الفاتورة</h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">العنوان</label>
                  <input
                    type="text"
                    value={editForm.title}
                    onChange={(e) => setEditForm({...editForm, title: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">المبلغ</label>
                  <input
                    type="number"
                    value={editForm.amount}
                    onChange={(e) => setEditForm({...editForm, amount: Number(e.target.value)})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">الحالة</label>
                  <select
                    value={editForm.status}
                    onChange={(e) => setEditForm({...editForm, status: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm"
                  >
                    <option value="pending">معلق</option>
                    <option value="paid">مدفوع</option>
                    <option value="overdue">متأخر</option>
                    <option value="draft">مسودة</option>
                  </select>
                </div>
              </div>
              <div className="flex gap-3 mt-6">
                <button onClick={handleSaveEdit} className="flex-1 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700">
                  حفظ
                </button>
                <button onClick={() => setShowEditModal(false)} className="flex-1 px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200">
                  إلغاء
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}