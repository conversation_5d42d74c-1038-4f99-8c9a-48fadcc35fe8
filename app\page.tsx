import Link from 'next/link'
import Header from './components/Header'
import Footer from './components/Footer'
import { 
  DocumentTextIcon,
  ChartBarIcon,
  FolderIcon,
  PlusCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  UserGroupIcon
} from '@heroicons/react/24/outline'

const services = [
  {
    id: 1,
    title: 'إدارة الفواتير',
    description: 'إدارة وتتبع جميع فواتيرك المالية بسهولة',
    icon: DocumentTextIcon,
    href: '/dashboard/invoices',
    color: 'bg-blue-50 text-blue-600',
    bgColor: 'hover:bg-blue-50'
  },
  {
    id: 2,
    title: 'تقارير الإنفاق',
    description: 'احصل على تقارير مفصلة عن نمط إنفاقك',
    icon: ChartBarIcon,
    href: '/dashboard/reports',
    color: 'bg-green-50 text-green-600',
    bgColor: 'hover:bg-green-50'
  },
  {
    id: 3,
    title: 'رفع المستندات',
    description: 'رفع وأرشفة مستنداتك المالية بأمان',
    icon: FolderIcon,
    href: '/dashboard/documents',
    color: 'bg-purple-50 text-purple-600',
    bgColor: 'hover:bg-purple-50'
  },
  {
    id: 4,
    title: 'تقديم طلب مالي',
    description: 'قدم طلبات السلف والخدمات المالية',
    icon: PlusCircleIcon,
    href: '/dashboard/requests',
    color: 'bg-orange-50 text-orange-600',
    bgColor: 'hover:bg-orange-50'
  },
  {
    id: 5,
    title: 'مراجعة العمليات السابقة',
    description: 'تتبع ومراجعة جميع عملياتك المالية',
    icon: ClockIcon,
    href: '/dashboard/history',
    color: 'bg-red-50 text-red-600',
    bgColor: 'hover:bg-red-50'
  },
  {
    id: 6,
    title: 'إعدادات الحساب',
    description: 'إدارة بياناتك الشخصية والأمان',
    icon: ShieldCheckIcon,
    href: '/dashboard/settings',
    color: 'bg-gray-50 text-gray-600',
    bgColor: 'hover:bg-gray-50'
  }
]

const stats = [
  {
    title: 'المستخدمين النشطين',
    count: '50,000+',
    icon: UserGroupIcon
  },
  {
    title: 'إجمالي المعاملات',
    count: '2.5M',
    icon: CurrencyDollarIcon
  },
  {
    title: 'معدل الأمان',
    count: '99.9%',
    icon: ShieldCheckIcon
  }
]

export default function HomePage() {
  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
              مرحبًا بك في منصة 
              <span className="text-primary-600"> متابعة مالية</span>
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
              منصتك الذكية لإدارة وتتبع كل ما يتعلق بأمورك المالية بدقة وأمان. 
              اختر الخدمة التي ترغب في الوصول إليها:
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/dashboard"
                className="btn-primary px-8 py-3 text-lg inline-block text-center"
              >
                ابدأ الآن
              </Link>
              <Link
                href="/services"
                className="btn-secondary px-8 py-3 text-lg inline-block text-center"
              >
                تصفح الخدمات
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">خدماتنا المالية</h2>
            <p className="text-lg text-gray-600">
              مجموعة شاملة من الخدمات المالية المصممة لتلبية احتياجاتك
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {services.map((service) => {
              const IconComponent = service.icon
              return (
                <Link key={service.id} href={service.href}>
                  <div className={`service-card ${service.bgColor} group`}>
                    <div className={`w-12 h-12 rounded-lg ${service.color} flex items-center justify-center mb-4`}>
                      <IconComponent className="w-6 h-6" />
                    </div>
                    <h3 className="text-xl font-semibold text-gray-900 mb-2 group-hover:text-primary-600 transition-colors">
                      {service.title}
                    </h3>
                    <p className="text-gray-600">
                      {service.description}
                    </p>
                  </div>
                </Link>
              )
            })}
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-gray-900 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">أرقام تتحدث عن نفسها</h2>
            <p className="text-lg text-gray-300">
              ثقة المستخدمين هي أولويتنا
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {stats.map((stat, index) => {
              const IconComponent = stat.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-primary-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-white" />
                  </div>
                  <div className="text-3xl font-bold text-white mb-2">{stat.count}</div>
                  <div className="text-gray-300">{stat.title}</div>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">لماذا متابعة مالية؟</h2>
            <p className="text-lg text-gray-600">
              مميزات تجعلنا الخيار الأمثل لإدارة أموالك
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ShieldCheckIcon className="w-8 h-8 text-primary-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">أمان عالي</h3>
              <p className="text-gray-600">
                نستخدم أحدث تقنيات التشفير لحماية بياناتك المالية
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ChartBarIcon className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">تقارير ذكية</h3>
              <p className="text-gray-600">
                احصل على تحليل مفصل لنمط إنفاقك مع توصيات ذكية
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <ClockIcon className="w-8 h-8 text-orange-600" />
              </div>
              <h3 className="text-xl font-semibold mb-2">متاح 24/7</h3>
              <p className="text-gray-600">
                وصول مستمر لحسابك وخدماتك في أي وقت ومن أي مكان
              </p>
            </div>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}