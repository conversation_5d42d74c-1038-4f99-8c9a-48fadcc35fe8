import './globals.css'
import type { Metadata } from 'next'
import { DataProvider } from './contexts/DataContext'
import { ToastProvider } from './contexts/ToastContext'
import { NotificationProvider } from './contexts/NotificationContext'

export const metadata: Metadata = {
  title: 'متابعة مالية - منصتك الذكية للإدارة المالية',
  description: 'منصة متابعة مالية تمكنك من إدارة ومتابعة وضعك المالي بدقة وأمان',
  keywords: 'متابعة مالية, إدارة مالية, تقارير مالية, أبشر مالي',
  authors: [{ name: 'Financial Tracker Team' }],
  robots: 'index, follow',
}

export const viewport = {
  width: 'device-width',
  initialScale: 1,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="ar" dir="rtl">
      <head>
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />
        <meta name="theme-color" content="#0284c7" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
      </head>
      <body className="font-arabic antialiased bg-gray-50">
        <DataProvider>
          <ToastProvider>
            <NotificationProvider>
              <div className="min-h-screen">
                {children}
              </div>
            </NotificationProvider>
          </ToastProvider>
        </DataProvider>
      </body>
    </html>
  )
}