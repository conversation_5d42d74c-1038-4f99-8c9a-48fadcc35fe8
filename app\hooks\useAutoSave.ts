'use client'

import { useEffect, useRef } from 'react'
import { useToastContext } from '@/app/contexts/ToastContext'

interface UseAutoSaveOptions {
  data: any
  saveFunction: (data: any) => void
  delay?: number
  enabled?: boolean
  onSave?: () => void
  onError?: (error: any) => void
}

export const useAutoSave = ({
  data,
  saveFunction,
  delay = 2000,
  enabled = true,
  onSave,
  onError
}: UseAutoSaveOptions) => {
  const { success, error: showError } = useToastContext()
  const timeoutRef = useRef<NodeJS.Timeout | null>(null)
  const previousDataRef = useRef<any>(null)

  useEffect(() => {
    if (!enabled) return

    // التحقق من تغيير البيانات
    const dataChanged = JSON.stringify(data) !== JSON.stringify(previousDataRef.current)
    
    if (dataChanged && previousDataRef.current !== null) {
      // إلغاء المؤقت السابق
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }

      // تعيين مؤقت جديد للحفظ
      timeoutRef.current = setTimeout(() => {
        try {
          saveFunction(data)
          success('تم حفظ التغييرات تلقائياً ✅', 1500)
          onSave?.()
        } catch (err) {
          console.error('Auto-save error:', err)
          showError('حدث خطأ في الحفظ التلقائي')
          onError?.(err)
        }
      }, delay)
    }

    // تحديث البيانات المرجعية
    previousDataRef.current = data

    // تنظيف المؤقت عند إلغاء التحميل
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [data, saveFunction, delay, enabled, success, showError, onSave, onError])

  // دالة للحفظ الفوري
  const saveNow = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    try {
      saveFunction(data)
      success('تم الحفظ بنجاح ✅')
      onSave?.()
    } catch (err) {
      console.error('Manual save error:', err)
      showError('حدث خطأ في الحفظ')
      onError?.(err)
    }
  }

  return { saveNow }
}