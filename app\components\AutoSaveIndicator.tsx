'use client'

import { useEffect, useState } from 'react'
import { CheckCircleIcon, ClockIcon } from '@heroicons/react/24/outline'

interface AutoSaveIndicatorProps {
  lastSaved?: Date | null
  isSaving?: boolean
}

export default function AutoSaveIndicator({ lastSaved, isSaving = false }: AutoSaveIndicatorProps) {
  const [timeAgo, setTimeAgo] = useState<string>('')

  useEffect(() => {
    if (!lastSaved) return

    const updateTimeAgo = () => {
      const now = new Date()
      const diffInSeconds = Math.floor((now.getTime() - lastSaved.getTime()) / 1000)
      
      if (diffInSeconds < 60) {
        setTimeAgo('الآن')
      } else if (diffInSeconds < 3600) {
        const minutes = Math.floor(diffInSeconds / 60)
        setTimeAgo(`منذ ${minutes} دقيقة`)
      } else {
        const hours = Math.floor(diffInSeconds / 3600)
        setTimeAgo(`منذ ${hours} ساعة`)
      }
    }

    updateTimeAgo()
    const interval = setInterval(updateTimeAgo, 60000) // تحديث كل دقيقة

    return () => clearInterval(interval)
  }, [lastSaved])

  if (!lastSaved && !isSaving) return null

  return (
    <div className="flex items-center space-x-2 space-x-reverse text-sm">
      {isSaving ? (
        <>
          <ClockIcon className="w-4 h-4 text-yellow-500 animate-spin" />
          <span className="text-yellow-600">جاري الحفظ...</span>
        </>
      ) : (
        <>
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
          <span className="text-green-600">
            تم الحفظ {timeAgo}
          </span>
        </>
      )}
    </div>
  )
}