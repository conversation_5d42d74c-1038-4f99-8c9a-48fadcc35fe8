'use client'

import { useState } from 'react'
import {
  PlusCircleIcon,
  DocumentTextIcon,
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  BanknotesIcon,
  ChartBarIcon,
  CreditCardIcon,
  ArrowPathIcon,
  EyeIcon,
  PencilIcon
} from '@heroicons/react/24/outline'

const requestTypes = [
  {
    id: 'loan',
    title: 'طلب سلفة',
    description: 'طلب سلفة مالية سريعة',
    icon: BanknotesIcon,
    color: 'bg-blue-50 text-blue-600',
    bgColor: 'hover:bg-blue-50'
  },
  {
    id: 'analysis',
    title: 'طلب تحليل مالي',
    description: 'تحليل مفصل للوضع المالي',
    icon: ChartBarIcon,
    color: 'bg-green-50 text-green-600',
    bgColor: 'hover:bg-green-50'
  },
  {
    id: 'credit',
    title: 'طلب بطاقة ائتمان',
    description: 'التقديم على بطاقة ائتمان جديدة',
    icon: CreditCardIcon,
    color: 'bg-purple-50 text-purple-600',
    bgColor: 'hover:bg-purple-50'
  },
  {
    id: 'refinance',
    title: 'طلب إعادة تمويل',
    description: 'إعادة هيكلة الديون الحالية',
    icon: ArrowPathIcon,
    color: 'bg-orange-50 text-orange-600',
    bgColor: 'hover:bg-orange-50'
  }
]

const existingRequests: any[] = []

const statusConfig = {
  pending: { label: 'قيد المراجعة', color: 'text-yellow-600 bg-yellow-50', icon: ClockIcon },
  in_progress: { label: 'قيد التنفيذ', color: 'text-blue-600 bg-blue-50', icon: ArrowPathIcon },
  approved: { label: 'تم الموافقة', color: 'text-green-600 bg-green-50', icon: CheckCircleIcon },
  rejected: { label: 'مرفوض', color: 'text-red-600 bg-red-50', icon: XCircleIcon }
}

export default function RequestsPage() {
  const [activeTab, setActiveTab] = useState('existing')
  const [selectedRequestType, setSelectedRequestType] = useState('')
  const [showRequestForm, setShowRequestForm] = useState(false)

  const getRequestStats = () => {
    return {
      total: existingRequests.length,
      pending: existingRequests.filter(req => req.status === 'pending').length,
      inProgress: existingRequests.filter(req => req.status === 'in_progress').length,
      approved: existingRequests.filter(req => req.status === 'approved').length,
      rejected: existingRequests.filter(req => req.status === 'rejected').length
    }
  }

  const stats = getRequestStats()

  const handleNewRequest = (type: string) => {
    setSelectedRequestType(type)
    setShowRequestForm(true)
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الطلبات المالية</h1>
          <p className="mt-1 text-sm text-gray-600">
            إدارة ومتابعة جميع طلباتك المالية
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
              <DocumentTextIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الطلبات</p>
              <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center ml-4">
              <ClockIcon className="w-6 h-6 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">قيد المراجعة</p>
              <p className="text-2xl font-bold text-yellow-600">{stats.pending}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center ml-4">
              <CheckCircleIcon className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">تمت الموافقة</p>
              <p className="text-2xl font-bold text-green-600">{stats.approved}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
              <ArrowPathIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">قيد التنفيذ</p>
              <p className="text-2xl font-bold text-blue-600">{stats.inProgress}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 space-x-reverse px-6">
            <button
              onClick={() => setActiveTab('existing')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'existing'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              الطلبات الحالية ({existingRequests.length})
            </button>
            <button
              onClick={() => setActiveTab('new')}
              className={`py-4 px-1 border-b-2 font-medium text-sm ${
                activeTab === 'new'
                  ? 'border-primary-500 text-primary-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              طلب جديد
            </button>
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'existing' ? (
            <div className="space-y-4">
              {existingRequests.map((request) => {
                const StatusIcon = statusConfig[request.status as keyof typeof statusConfig].icon
                const requestTypeInfo = requestTypes.find(type => type.id === request.type)
                const RequestIcon = requestTypeInfo?.icon || DocumentTextIcon

                return (
                  <div key={request.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 space-x-reverse">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${requestTypeInfo?.color || 'bg-gray-100'}`}>
                          <RequestIcon className="w-6 h-6" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 space-x-reverse">
                            <h3 className="text-lg font-medium text-gray-900">{request.title}</h3>
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig[request.status as keyof typeof statusConfig].color}`}>
                              <StatusIcon className="w-4 h-4 ml-1" />
                              {statusConfig[request.status as keyof typeof statusConfig].label}
                            </span>
                          </div>
                          <p className="text-sm text-gray-600 mt-1">رقم الطلب: {request.requestNumber}</p>
                          {request.amount > 0 && (
                            <p className="text-lg font-semibold text-primary-600 mt-2">
                              {request.amount.toLocaleString()} ر.س
                            </p>
                          )}
                          <div className="flex items-center space-x-6 space-x-reverse text-sm text-gray-500 mt-3">
                            <span>تاريخ التقديم: {request.submittedDate}</span>
                            <span>آخر تحديث: {request.updatedDate}</span>
                          </div>
                          <div className="mt-3">
                            <p className="text-sm text-gray-700 bg-gray-50 p-3 rounded-lg">
                              {request.notes}
                            </p>
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2 space-x-reverse">
                        <button className="text-primary-600 hover:text-primary-700 p-2 rounded-lg hover:bg-primary-50">
                          <EyeIcon className="w-4 h-4" />
                        </button>
                        {request.status === 'pending' && (
                          <button className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50">
                            <PencilIcon className="w-4 h-4" />
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="space-y-6">
              <div>
                <h2 className="text-lg font-medium text-gray-900 mb-4">اختر نوع الطلب</h2>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {requestTypes.map((type) => {
                    const IconComponent = type.icon
                    return (
                      <button
                        key={type.id}
                        onClick={() => handleNewRequest(type.id)}
                        className={`p-6 border border-gray-200 rounded-lg text-right hover:border-primary-300 hover:shadow-md transition-all ${type.bgColor}`}
                      >
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${type.color}`}>
                            <IconComponent className="w-6 h-6" />
                          </div>
                          <div>
                            <h3 className="text-lg font-medium text-gray-900">{type.title}</h3>
                            <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                          </div>
                        </div>
                      </button>
                    )
                  })}
                </div>
              </div>

              {showRequestForm && (
                <div className="border-t border-gray-200 pt-6">
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    نموذج {requestTypes.find(type => type.id === selectedRequestType)?.title}
                  </h3>
                  <form className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          عنوان الطلب
                        </label>
                        <input
                          type="text"
                          className="input-field"
                          placeholder="أدخل عنوان مناسب للطلب"
                        />
                      </div>
                      {selectedRequestType === 'loan' && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">
                            المبلغ المطلوب (ر.س)
                          </label>
                          <input
                            type="number"
                            className="input-field"
                            placeholder="0"
                          />
                        </div>
                      )}
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تفاصيل الطلب
                      </label>
                      <textarea
                        rows={4}
                        className="input-field"
                        placeholder="اشرح تفاصيل طلبك بوضوح..."
                      ></textarea>
                    </div>
                    <div className="flex items-center space-x-4 space-x-reverse">
                      <button type="submit" className="btn-primary">
                        تقديم الطلب
                      </button>
                      <button
                        type="button"
                        onClick={() => setShowRequestForm(false)}
                        className="btn-secondary"
                      >
                        إلغاء
                      </button>
                    </div>
                  </form>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}