'use client'

import Link from 'next/link'
import Header from './components/Header'
import Footer from './components/Footer'
import { MartyrHeroSection } from './components/MartyrTribute'
import {
  DocumentTextIcon,
  ChartBarIcon,
  FolderIcon,
  PlusCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  SparklesIcon,
  RocketLaunchIcon,
  BoltIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import {
  ScrollReveal,
  HoverLift,
  SuperCard,
  AnimatedCounter,
  FloatingParticles,
  GlowText,
  SuperButton
} from './components/SuperAnimations'

const services = [
  {
    id: 1,
    title: 'إدارة الفواتير',
    description: 'إدارة وتتبع جميع فواتيرك المالية بسهولة مع تقنيات متطورة',
    icon: DocumentTextIcon,
    href: '/dashboard/invoices',
    color: 'bg-gradient-to-br from-electric-50 to-electric-100 text-electric-700 border-electric-200',
    bgColor: 'hover:from-electric-100 hover:to-electric-200 hover:shadow-glow',
    iconColor: 'text-electric-600'
  },
  {
    id: 2,
    title: 'تقارير الإنفاق',
    description: 'احصل على تقارير مفصلة وتحليلات ذكية لنمط إنفاقك',
    icon: ChartBarIcon,
    href: '/dashboard/reports',
    color: 'bg-gradient-to-br from-neon-50 to-neon-100 text-neon-700 border-neon-200',
    bgColor: 'hover:from-neon-100 hover:to-neon-200 hover:shadow-neon',
    iconColor: 'text-neon-600'
  },
  {
    id: 3,
    title: 'رفع المستندات',
    description: 'رفع وأرشفة مستنداتك المالية بأمان عالي وتشفير متقدم',
    icon: FolderIcon,
    href: '/dashboard/documents',
    color: 'bg-gradient-to-br from-cyber-50 to-cyber-100 text-cyber-700 border-cyber-200',
    bgColor: 'hover:from-cyber-100 hover:to-cyber-200 hover:shadow-cyber',
    iconColor: 'text-cyber-600'
  },
  {
    id: 4,
    title: 'تقديم طلب مالي',
    description: 'قدم طلبات السلف والخدمات المالية بسرعة وسهولة',
    icon: PlusCircleIcon,
    href: '/dashboard/requests',
    color: 'bg-gradient-to-br from-gold-50 to-gold-100 text-gold-700 border-gold-200',
    bgColor: 'hover:from-gold-100 hover:to-gold-200 hover:shadow-glow-lg',
    iconColor: 'text-gold-600'
  },
  {
    id: 5,
    title: 'مراجعة العمليات السابقة',
    description: 'تتبع ومراجعة جميع عملياتك المالية مع تحليل زمني متقدم',
    icon: ClockIcon,
    href: '/dashboard/history',
    color: 'bg-gradient-to-br from-primary-50 to-primary-100 text-primary-700 border-primary-200',
    bgColor: 'hover:from-primary-100 hover:to-primary-200 hover:shadow-glow',
    iconColor: 'text-primary-600'
  },
  {
    id: 6,
    title: 'إعدادات الحساب',
    description: 'إدارة بياناتك الشخصية والأمان مع حماية متعددة الطبقات',
    icon: ShieldCheckIcon,
    href: '/dashboard/settings',
    color: 'bg-gradient-to-br from-electric-50 to-electric-100 text-electric-700 border-electric-200',
    bgColor: 'hover:from-electric-100 hover:to-electric-200 hover:shadow-glow',
    iconColor: 'text-electric-600'
  }
]

const stats = [
  {
    title: 'المستخدمين النشطين',
    count: '50,000+',
    icon: UserGroupIcon,
    color: 'neon',
    description: 'مستخدم يثق بنا'
  },
  {
    title: 'إجمالي المعاملات',
    count: '2.5M',
    icon: CurrencyDollarIcon,
    color: 'gold',
    description: 'معاملة آمنة'
  },
  {
    title: 'معدل الأمان',
    count: '99.9%',
    icon: ShieldCheckIcon,
    color: 'electric',
    description: 'حماية متقدمة'
  }
]

const features = [
  {
    icon: SparklesIcon,
    title: 'تقنيات متطورة',
    description: 'أحدث التقنيات في عالم التكنولوجيا المالية',
    color: 'electric'
  },
  {
    icon: RocketLaunchIcon,
    title: 'أداء فائق السرعة',
    description: 'معالجة فورية للمعاملات والتقارير',
    color: 'cyber'
  },
  {
    icon: BoltIcon,
    title: 'طاقة لا محدودة',
    description: 'قدرات حاسوبية عالية لتحليل البيانات',
    color: 'gold'
  }
]

export default function HomePage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-electric-50">
      <Header />

      {/* Hero Section للشهيد */}
      <MartyrHeroSection />

      {/* قسم الميزات الخارقة */}
      <section className="py-20 relative overflow-hidden">
        <div className="absolute inset-0 bg-hero-pattern opacity-30"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-6 animate-fade-in">
              <span className="bg-gradient-to-r from-electric-600 to-cyber-600 bg-clip-text text-transparent">
                ميزات خارقة
              </span>
              <span className="text-gray-800"> لإدارة مالية متطورة</span>
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto animate-slide-up">
              تقنيات متقدمة وحلول ذكية لتجربة مالية لا مثيل لها
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8 mb-16">
            {features.map((feature, index) => (
              <div
                key={index}
                className={`p-8 rounded-2xl bg-gradient-to-br from-${feature.color}-50 to-${feature.color}-100 border border-${feature.color}-200 hover:shadow-glow transition-all duration-500 animate-scale-in group`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r from-${feature.color}-400 to-${feature.color}-600 flex items-center justify-center mb-6 group-hover:animate-float`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h3 className={`text-xl font-bold text-${feature.color}-800 mb-4`}>{feature.title}</h3>
                <p className={`text-${feature.color}-700`}>{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* قسم الخدمات الرئيسية */}
      <section className="py-20 bg-gradient-to-br from-dark-100 via-dark-200 to-neon-100 relative overflow-hidden">
        <div className="absolute inset-0 bg-black-hero bg-cover opacity-40"></div>
        <div className="absolute inset-0 bg-dark-grid bg-grid opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 animate-fade-in glow-text">
              خدماتنا المتطورة
            </h2>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto animate-slide-up">
              منصتك الذكية لإدارة وتتبع كل ما يتعلق بأمورك المالية بدقة وأمان
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <ScrollReveal delay={300}>
                <Link href="/dashboard">
                  <SuperButton variant="primary" className="px-8 py-3 text-lg">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <RocketLaunchIcon className="w-5 h-5" />
                      <span>ابدأ الآن</span>
                    </div>
                  </SuperButton>
                </Link>
              </ScrollReveal>
              <ScrollReveal delay={400}>
                <Link href="/services">
                  <SuperButton variant="secondary" className="px-8 py-3 text-lg">
                    <div className="flex items-center space-x-2 space-x-reverse">
                      <SparklesIcon className="w-5 h-5" />
                      <span>تصفح الخدمات</span>
                    </div>
                  </SuperButton>
                </Link>
              </ScrollReveal>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service, index) => {
              const IconComponent = service.icon
              return (
                <ScrollReveal key={service.id} delay={index * 100}>
                  <HoverLift>
                    <Link href={service.href}>
                      <SuperCard
                        className={`relative p-8 rounded-2xl ${service.color} border-2 ${service.bgColor} transition-all duration-500 group`}
                        glowColor={service.color.includes('electric') ? 'electric' :
                                  service.color.includes('neon') ? 'neon' :
                                  service.color.includes('cyber') ? 'cyber' : 'gold'}
                      >
                        <div className="relative">
                          <div className={`w-16 h-16 rounded-2xl ${service.iconColor} bg-white/90 backdrop-blur-sm flex items-center justify-center mb-6 group-hover:animate-bounce-slow shadow-lg float-superior`}>
                            <IconComponent className="w-8 h-8" />
                          </div>
                          <GlowText>
                            <h3 className="text-xl font-bold mb-3 group-hover:animate-pulse">
                              {service.title}
                            </h3>
                          </GlowText>
                          <p className="text-sm opacity-90 leading-relaxed">
                            {service.description}
                          </p>
                          <div className="mt-6 flex items-center text-sm font-medium opacity-75 group-hover:opacity-100 transition-opacity">
                            <span>اكتشف المزيد</span>
                            <ArrowRightIcon className="w-4 h-4 mr-2 group-hover:translate-x-1 transition-transform" />
                          </div>
                        </div>
                      </SuperCard>
                    </Link>
                  </HoverLift>
                </ScrollReveal>
              )
            })}
          </div>
        </div>
      </section>

      {/* قسم الإحصائيات الخارقة */}
      <section className="py-20 bg-gradient-to-br from-dark-50 via-dark-100 to-dark-200 relative overflow-hidden">
        <div className="absolute inset-0 bg-dark-grid bg-grid opacity-30"></div>
        <div className="absolute inset-0 bg-black-hero bg-cover opacity-20"></div>
        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-white mb-6 animate-fade-in glow-text">
              أرقام تتحدث عن نفسها
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto animate-slide-up">
              إنجازات حقيقية تعكس ثقة عملائنا وجودة خدماتنا
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {stats.map((stat, index) => (
              <ScrollReveal key={index} delay={index * 200}>
                <SuperCard
                  className={`text-center p-8 rounded-2xl bg-gradient-to-br from-${stat.color}-500/20 to-${stat.color}-600/20 border border-${stat.color}-400/30 backdrop-blur-sm transition-all duration-500 group`}
                  glowColor={stat.color}
                >
                  <FloatingParticles count={8} color={stat.color} />
                  <div className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r from-${stat.color}-400 to-${stat.color}-600 flex items-center justify-center group-hover:animate-pulse-slow float-superior`}>
                    <stat.icon className="w-10 h-10 text-white" />
                  </div>
                  <div className={`text-4xl md:text-5xl font-bold text-${stat.color}-400 mb-2`}>
                    <GlowText>
                      <AnimatedCounter
                        end={parseInt(stat.count.replace(/[^\d]/g, ''))}
                        suffix={stat.count.includes('+') ? '+' : stat.count.includes('%') ? '%' : ''}
                        duration={2000 + index * 500}
                      />
                    </GlowText>
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-2">{stat.title}</h3>
                  <p className="text-gray-300 text-sm">{stat.description}</p>
                </SuperCard>
              </ScrollReveal>
            ))}
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}