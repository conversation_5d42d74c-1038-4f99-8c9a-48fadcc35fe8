'use client'

import { useState } from 'react'
import {
  ArrowTrendingUpIcon as TrendingUpIcon,
  ArrowTrendingDownIcon as TrendingDownIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  PlusIcon,
  EyeIcon,
  BuildingLibraryIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'

export default function InvestmentsPage() {
  const [investments] = useState([
    {
      id: '1',
      name: 'صندوق الراجحي للأسهم',
      type: 'صندوق استثماري',
      amount: 50000,
      currentValue: 52500,
      change: 2500,
      changePercent: 5.0,
      icon: BuildingLibraryIcon
    },
    {
      id: '2', 
      name: 'أسهم أرامكو',
      type: 'أسهم محلية',
      amount: 30000,
      currentValue: 28500,
      change: -1500,
      changePercent: -5.0,
      icon: TrendingDownIcon
    },
    {
      id: '3',
      name: 'صكوك حكومية',
      type: 'دخل ثابت',
      amount: 100000,
      currentValue: 103000,
      change: 3000,
      changePercent: 3.0,
      icon: GlobeAltIcon
    }
  ])

  const totalInvested = investments.reduce((sum, inv) => sum + inv.amount, 0)
  const totalCurrent = investments.reduce((sum, inv) => sum + inv.currentValue, 0)
  const totalGainLoss = totalCurrent - totalInvested
  const totalGainLossPercent = (totalGainLoss / totalInvested) * 100

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">محفظة الاستثمارات</h1>
          <p className="text-gray-600">تتبع وإدارة استثماراتك المالية</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          استثمار جديد
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المستثمر</p>
              <p className="text-2xl font-bold text-blue-600">{totalInvested.toLocaleString()} ر.س</p>
            </div>
            <CurrencyDollarIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">القيمة الحالية</p>
              <p className="text-2xl font-bold text-purple-600">{totalCurrent.toLocaleString()} ر.س</p>
            </div>
            <ChartBarIcon className="w-8 h-8 text-purple-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">الربح/الخسارة</p>
              <p className={`text-2xl font-bold ${totalGainLoss >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {totalGainLoss >= 0 ? '+' : ''}{totalGainLoss.toLocaleString()} ر.س
              </p>
            </div>
            {totalGainLoss >= 0 ? 
              <TrendingUpIcon className="w-8 h-8 text-green-600" /> :
              <TrendingDownIcon className="w-8 h-8 text-red-600" />
            }
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">نسبة العائد</p>
              <p className={`text-2xl font-bold ${totalGainLossPercent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {totalGainLossPercent >= 0 ? '+' : ''}{totalGainLossPercent.toFixed(2)}%
              </p>
            </div>
            {totalGainLossPercent >= 0 ? 
              <TrendingUpIcon className="w-8 h-8 text-green-600" /> :
              <TrendingDownIcon className="w-8 h-8 text-red-600" />
            }
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">استثماراتي</h2>
        </div>
        <div className="divide-y">
          {investments.map((investment) => {
            const IconComponent = investment.icon
            const isPositive = investment.change >= 0
            
            return (
              <div key={investment.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <IconComponent className="w-6 h-6 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{investment.name}</h3>
                      <p className="text-sm text-gray-500">{investment.type}</p>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <div className="flex items-center gap-4">
                      <div>
                        <p className="text-sm text-gray-600">المبلغ المستثمر</p>
                        <p className="font-medium">{investment.amount.toLocaleString()} ر.س</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">القيمة الحالية</p>
                        <p className="font-medium">{investment.currentValue.toLocaleString()} ر.س</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">الربح/الخسارة</p>
                        <div className="flex items-center gap-1">
                          {isPositive ? 
                            <TrendingUpIcon className="w-4 h-4 text-green-500" /> :
                            <TrendingDownIcon className="w-4 h-4 text-red-500" />
                          }
                          <span className={`font-medium ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                            {isPositive ? '+' : ''}{investment.change.toLocaleString()} ر.س
                          </span>
                          <span className={`text-sm ${isPositive ? 'text-green-600' : 'text-red-600'}`}>
                            ({isPositive ? '+' : ''}{investment.changePercent.toFixed(1)}%)
                          </span>
                        </div>
                      </div>
                      <button className="p-2 text-gray-600 hover:bg-gray-100 rounded">
                        <EyeIcon className="w-4 h-4" />
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold mb-4">توزيع المحفظة</h3>
          <div className="space-y-4">
            {investments.map((inv, index) => {
              const percentage = (inv.currentValue / totalCurrent) * 100
              const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-yellow-500']
              
              return (
                <div key={inv.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`}></div>
                    <span className="text-sm font-medium">{inv.name}</span>
                  </div>
                  <span className="text-sm text-gray-600">{percentage.toFixed(1)}%</span>
                </div>
              )
            })}
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-6 border border-green-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📈 نصائح استثمارية</h3>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">نوع استثماراتك لتقليل المخاطر</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">استثمر على المدى الطويل</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">راجع محفظتك بانتظام</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">لا تستثمر أكثر مما تستطيع خسارته</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}