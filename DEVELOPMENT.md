# دليل التطوير - موقع متابعة مالية

## 🚀 البدء السريع

### الطريقة الأولى: ملف التشغيل التلقائي

**Windows:**
```bash
./start.bat
```

**Linux/Mac:**
```bash
chmod +x start.sh
./start.sh
```

### الطريقة الثانية: الخطوات اليدوية

1. **تثبيت Node.js**
   - تحميل من: https://nodejs.org
   - الإصدار المطلوب: 18 أو أحدث

2. **تثبيت الحزم**
   ```bash
   npm install
   ```

3. **تشغيل الخادم المحلي**
   ```bash
   npm run dev
   ```

4. **فتح المتصفح**
   - انتقل إلى: http://localhost:3000

## 📁 هيكل المشروع

```
📦 موقع زي أبشر/
├── 📁 app/                     # تطبيق Next.js الرئيسي
│   ├── 📁 components/          # المكونات المشتركة
│   │   ├── Header.tsx          # شريط التنقل العلوي
│   │   ├── Footer.tsx          # تذييل الصفحة
│   │   ├── Modal.tsx           # مكون النافذة المنبثقة
│   │   └── LoadingSpinner.tsx  # مكون التحميل
│   │
│   ├── 📁 auth/                # المصادقة وتسجيل الدخول
│   │   └── 📁 login/
│   │       └── page.tsx        # صفحة تسجيل الدخول
│   │
│   ├── 📁 dashboard/           # لوحة التحكم
│   │   ├── layout.tsx          # تخطيط لوحة التحكم
│   │   ├── page.tsx            # الصفحة الرئيسية للوحة
│   │   ├── 📁 reports/         # التقارير المالية
│   │   ├── 📁 invoices/        # إدارة الفواتير
│   │   ├── 📁 requests/        # الطلبات المالية
│   │   ├── 📁 documents/       # إدارة المستندات
│   │   └── 📁 settings/        # الإعدادات
│   │
│   ├── globals.css             # الأنماط العامة
│   ├── layout.tsx              # التخطيط الرئيسي
│   └── page.tsx                # الصفحة الرئيسية
│
├── 📁 public/                  # الملفات العامة (صور، أيقونات)
├── tailwind.config.js          # إعدادات Tailwind CSS
├── next.config.js              # إعدادات Next.js
├── tsconfig.json              # إعدادات TypeScript
├── package.json               # معلومات المشروع والحزم
├── README.md                  # وثائق المشروع
├── .env.example               # مثال على متغيرات البيئة
├── start.bat                  # ملف تشغيل Windows
└── start.sh                   # ملف تشغيل Linux/Mac
```

## 🛠️ تقنيات المشروع

### Frontend
- **Next.js 14**: إطار العمل الرئيسي
- **TypeScript**: لغة البرمجة المستخدمة
- **Tailwind CSS**: للتصميم والأنماط
- **Heroicons**: الأيقونات

### الخطوط والألوان
- **الخط**: Tajawal (مخصص للعربية)
- **الألوان الأساسية**:
  - أزرق: `#0284c7` (primary)
  - أخضر: `#22c55e` (success)
  - أحمر: `#ef4444` (danger)
  - برتقالي: `#f59e0b` (warning)

## 🔧 الأوامر المتاحة

```bash
# تشغيل الخادم المحلي
npm run dev

# بناء المشروع للإنتاج
npm run build

# تشغيل المشروع المبني
npm run start

# فحص الكود
npm run lint

# فحص أنواع البيانات
npm run type-check

# تنظيف ملفات البناء
npm run clean
```

## 📋 بيانات تجريبية للاختبار

### تسجيل الدخول
- **البريد الإلكتروني**: أي بريد صحيح
- **كلمة المرور**: أي كلمة مرور
- يمكن استخدام أي بيانات للاختبار

### البيانات المعروضة
جميع البيانات المعروضة في الموقع هي بيانات تجريبية وهمية للعرض فقط.

## 🎨 إضافة صفحة جديدة

### 1. إنشاء مجلد جديد
```bash
mkdir app/dashboard/new-page
```

### 2. إنشاء الصفحة
```typescript
// app/dashboard/new-page/page.tsx
export default function NewPage() {
  return (
    <div>
      <h1>الصفحة الجديدة</h1>
    </div>
  )
}
```

### 3. إضافة التنقل (اختياري)
في `app/dashboard/layout.tsx`، أضف الصفحة للقائمة:
```typescript
const navigation = [
  // ... الصفحات الموجودة
  { name: 'صفحة جديدة', href: '/dashboard/new-page', icon: YourIcon }
]
```

## 🎯 إضافة مكون جديد

### 1. إنشاء المكون
```typescript
// app/components/YourComponent.tsx
interface YourComponentProps {
  title: string
}

export default function YourComponent({ title }: YourComponentProps) {
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <h2>{title}</h2>
    </div>
  )
}
```

### 2. استخدام المكون
```typescript
import YourComponent from '@/components/YourComponent'

// في أي صفحة
<YourComponent title="عنوان المكون" />
```

## 🔍 نصائح التطوير

### 1. استخدام Tailwind CSS
```typescript
// أمثلة على الكلاسات المفيدة
<div className="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
  <h2 className="text-lg font-semibold text-gray-900 mb-4">العنوان</h2>
  <p className="text-gray-600">المحتوى</p>
</div>
```

### 2. دعم RTL
```typescript
// استخدم space-x-reverse للمسافات
<div className="flex items-center space-x-4 space-x-reverse">
  <Icon />
  <span>النص</span>
</div>
```

### 3. الأيقونات
```typescript
import { HomeIcon } from '@heroicons/react/24/outline'

<HomeIcon className="w-5 h-5 text-gray-600" />
```

## 🚀 النشر على الإنتاج

### Vercel (مُوصى به)
1. ارفع المشروع على GitHub
2. اربط مع Vercel
3. النشر التلقائي

### خادم مخصص
```bash
npm run build
npm start
```

## 🐛 حل المشاكل الشائعة

### خطأ في تثبيت الحزم
```bash
# احذف node_modules وأعد التثبيت
rm -rf node_modules package-lock.json
npm install
```

### خطأ في TypeScript
```bash
# فحص الأخطاء
npm run type-check
```

### خطأ في التصميم
- تأكد من استخدام الكلاسات الصحيحة لـ Tailwind
- تحقق من دعم RTL في CSS

## 📞 الدعم والمساعدة

إذا واجهت أي مشاكل:
1. تحقق من هذا الدليل أولاً
2. راجع README.md
3. تحقق من ملفات المشروع للأمثلة

---

**ملاحظة**: هذا المشروع مصمم للتطوير والاختبار. لا تستخدم بيانات حقيقية أو حساسة.