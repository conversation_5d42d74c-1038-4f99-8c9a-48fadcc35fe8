/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    './pages/**/*.{js,ts,jsx,tsx,mdx}',
    './components/**/*.{js,ts,jsx,tsx,mdx}',
    './app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc', 
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        success: {
          50: '#f0fdf4',
          500: '#22c55e',
          600: '#16a34a',
        },
        warning: {
          50: '#fffbeb',
          500: '#f59e0b',
          600: '#d97706',
        },
        danger: {
          50: '#fef2f2',
          500: '#ef4444',
          600: '#dc2626',
        },
        // ألوان سوداء خارقة
        electric: {
          50: '#0a0a0f',
          100: '#1a1a2e',
          200: '#16213e',
          300: '#0f3460',
          400: '#0e4b99',
          500: '#2563eb',
          600: '#3b82f6',
          700: '#60a5fa',
          800: '#93c5fd',
          900: '#dbeafe',
        },
        neon: {
          50: '#0a0f0a',
          100: '#0d1f0d',
          200: '#14532d',
          300: '#166534',
          400: '#15803d',
          500: '#16a34a',
          600: '#22c55e',
          700: '#4ade80',
          800: '#86efac',
          900: '#bbf7d0',
        },
        cyber: {
          50: '#0f0a0f',
          100: '#1f0d1f',
          200: '#4c1d4c',
          300: '#701a75',
          400: '#86198f',
          500: '#a21caf',
          600: '#c026d3',
          700: '#d946ef',
          800: '#e879f9',
          900: '#f0abfc',
        },
        gold: {
          50: '#0f0f0a',
          100: '#1f1f0d',
          200: '#4d4d1a',
          300: '#78350f',
          400: '#92400e',
          500: '#b45309',
          600: '#d97706',
          700: '#f59e0b',
          800: '#fbbf24',
          900: '#fcd34d',
        },
        // ألوان سوداء أساسية
        dark: {
          50: '#0a0a0a',
          100: '#1a1a1a',
          200: '#2a2a2a',
          300: '#3a3a3a',
          400: '#4a4a4a',
          500: '#5a5a5a',
          600: '#6a6a6a',
          700: '#7a7a7a',
          800: '#8a8a8a',
          900: '#9a9a9a',
        }
      },
      fontFamily: {
        'arabic': ['Tajawal', 'system-ui', 'sans-serif'],
      },
      backgroundImage: {
        'gradient-radial': 'radial-gradient(var(--tw-gradient-stops))',
        'gradient-conic': 'conic-gradient(from 180deg at 50% 50%, var(--tw-gradient-stops))',
        'cyber-grid': 'linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px), linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px)',
        'hero-pattern': 'radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.2) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(217, 70, 239, 0.2) 0%, transparent 50%)',
        'martyr-gradient': 'linear-gradient(135deg, rgba(22, 163, 74, 0.1) 0%, rgba(59, 130, 246, 0.1) 50%, rgba(217, 70, 239, 0.1) 100%)',
        'dark-grid': 'linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px), linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px)',
        'black-hero': 'radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%), radial-gradient(circle at 75% 75%, rgba(34, 197, 94, 0.1) 0%, transparent 50%)',
      },
      backgroundSize: {
        'grid': '20px 20px',
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'pulse-slow': 'pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'bounce-slow': 'bounce 3s infinite',
        'spin-slow': 'spin 8s linear infinite',
        'gradient': 'gradient 15s ease infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
        'slide-up': 'slideUp 0.5s ease-out',
        'slide-down': 'slideDown 0.5s ease-out',
        'fade-in': 'fadeIn 0.6s ease-out',
        'scale-in': 'scaleIn 0.4s ease-out',
        'martyr-glow': 'martyrGlow 3s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': { transform: 'translateY(0px)' },
          '50%': { transform: 'translateY(-20px)' },
        },
        gradient: {
          '0%, 100%': {
            'background-size': '200% 200%',
            'background-position': 'left center'
          },
          '50%': {
            'background-size': '200% 200%',
            'background-position': 'right center'
          },
        },
        glow: {
          '0%': { 'box-shadow': '0 0 5px rgba(99, 102, 241, 0.5)' },
          '100%': { 'box-shadow': '0 0 20px rgba(99, 102, 241, 0.8), 0 0 30px rgba(99, 102, 241, 0.4)' },
        },
        slideUp: {
          '0%': { transform: 'translateY(100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        slideDown: {
          '0%': { transform: 'translateY(-100%)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.9)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
        martyrGlow: {
          '0%, 100%': {
            'box-shadow': '0 0 10px rgba(34, 197, 94, 0.3), 0 0 20px rgba(34, 197, 94, 0.1)',
            'border-color': 'rgba(34, 197, 94, 0.3)'
          },
          '50%': {
            'box-shadow': '0 0 20px rgba(34, 197, 94, 0.6), 0 0 40px rgba(34, 197, 94, 0.3)',
            'border-color': 'rgba(34, 197, 94, 0.6)'
          },
        },
      },
      boxShadow: {
        'glow': '0 0 20px rgba(99, 102, 241, 0.3)',
        'glow-lg': '0 0 40px rgba(99, 102, 241, 0.4)',
        'neon': '0 0 5px rgba(34, 197, 94, 0.5), 0 0 20px rgba(34, 197, 94, 0.3)',
        'cyber': '0 0 10px rgba(217, 70, 239, 0.5), 0 0 30px rgba(217, 70, 239, 0.2)',
        'inner-glow': 'inset 0 0 20px rgba(99, 102, 241, 0.1)',
        'martyr': '0 0 15px rgba(34, 197, 94, 0.4), 0 0 30px rgba(34, 197, 94, 0.2)',
      },
      backdropBlur: {
        xs: '2px',
      },
    },
  },
  plugins: [],
}