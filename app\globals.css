@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@200;300;400;500;700;800;900&display=swap');

:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  /* متغيرات الألوان الخارقة */
  --electric-glow: 0 0 20px rgba(99, 102, 241, 0.5);
  --neon-glow: 0 0 20px rgba(34, 197, 94, 0.5);
  --cyber-glow: 0 0 20px rgba(217, 70, 239, 0.5);
  --gold-glow: 0 0 20px rgba(245, 158, 11, 0.5);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  font-family: 'Tajawal', system-ui, sans-serif;
  direction: rtl;
}

body {
  color: rgb(var(--foreground-rgb));
  background: linear-gradient(
      to bottom,
      transparent,
      rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
}

@layer components {
  .btn-primary {
    @apply bg-primary-600 hover:bg-primary-700 text-white font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2;
  }

  .btn-secondary {
    @apply bg-gray-200 hover:bg-gray-300 text-gray-800 font-medium py-2 px-4 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
  }

  .input-field {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-colors duration-200;
  }

  .card {
    @apply bg-white rounded-lg shadow-md border border-gray-200 p-6;
  }

  .primary-100 {
    background-color: var(--primary-100);
  }

  .primary-700 {
    color: var(--primary-700);
  }

  .primary-600 {
    background-color: var(--primary-600);
  }

  .service-card {
    @apply bg-white p-6 rounded-lg shadow-md border border-gray-200 transition-all duration-200 hover:shadow-lg hover:border-primary-200 cursor-pointer;
  }
}

/* RTL Support */
[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* ===== انيميشن وتأثيرات خارقة ===== */

/* انيميشن خارق للتحميل */
@keyframes superLoading {
  0% {
    transform: rotate(0deg) scale(1);
    box-shadow: var(--electric-glow);
  }
  25% {
    transform: rotate(90deg) scale(1.1);
    box-shadow: var(--neon-glow);
  }
  50% {
    transform: rotate(180deg) scale(1);
    box-shadow: var(--cyber-glow);
  }
  75% {
    transform: rotate(270deg) scale(1.1);
    box-shadow: var(--gold-glow);
  }
  100% {
    transform: rotate(360deg) scale(1);
    box-shadow: var(--electric-glow);
  }
}

/* تأثيرات الوهج المتقدمة */
@keyframes pulseGlow {
  0%, 100% {
    box-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
  }
  50% {
    box-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
  }
}

@keyframes floatSuperior {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  33% {
    transform: translateY(-10px) rotate(1deg);
  }
  66% {
    transform: translateY(-5px) rotate(-1deg);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes morphing {
  0%, 100% {
    border-radius: 20px;
    transform: scale(1);
  }
  25% {
    border-radius: 50px;
    transform: scale(1.05);
  }
  50% {
    border-radius: 30px;
    transform: scale(0.95);
  }
  75% {
    border-radius: 40px;
    transform: scale(1.02);
  }
}

/* كلاسات مخصصة للانيميشن */
.super-loading {
  animation: superLoading 2s linear infinite;
}

.pulse-glow {
  animation: pulseGlow 2s ease-in-out infinite;
}

.float-superior {
  animation: floatSuperior 6s ease-in-out infinite;
}

.shimmer-effect {
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
  background-size: 200% 100%;
  animation: shimmer 2s infinite;
}

.morphing-card {
  animation: morphing 8s ease-in-out infinite;
}

/* تأثيرات التمرير */
.scroll-reveal {
  opacity: 0;
  transform: translateY(50px);
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* تأثيرات الهوفر المتقدمة */
.hover-lift {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.hover-lift:hover {
  transform: translateY(-10px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0,0,0,0.1);
}

/* تأثيرات الخلفية المتحركة */
.animated-bg {
  background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* تأثيرات النص المتحركة */
.text-glow {
  text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
}

.typing-effect {
  overflow: hidden;
  border-right: 2px solid;
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: currentColor; }
}

/* تأثيرات الأزرار الخارقة */
.btn-super {
  position: relative;
  overflow: hidden;
  background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 15px;
  padding: 12px 30px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-super::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
  transition: left 0.5s;
}

.btn-super:hover::before {
  left: 100%;
}

.btn-super:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.2);
}