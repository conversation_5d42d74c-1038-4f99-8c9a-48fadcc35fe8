'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import {
  ChartBarIcon,
  ArrowTrendingUpIcon as TrendingUpIcon,
  ArrowTrendingDownIcon as TrendingDownIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ArrowPathIcon,
  EyeIcon,
  DocumentChartBarIcon
} from '@heroicons/react/24/outline'

export default function AnalyticsPage() {
  const { userData } = useData()
  const [selectedPeriod, setSelectedPeriod] = useState('month')

  const transactions = userData?.transactions || []
  
  const totalIncome = transactions.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0)
  const totalExpenses = transactions.filter(t => t.amount < 0).reduce((sum, t) => sum + Math.abs(t.amount), 0)
  const netIncome = totalIncome - totalExpenses
  
  const categoryAnalysis = transactions.reduce((acc, transaction) => {
    const category = transaction.category
    if (!acc[category]) {
      acc[category] = { income: 0, expenses: 0, count: 0 }
    }
    if (transaction.amount > 0) {
      acc[category].income += transaction.amount
    } else {
      acc[category].expenses += Math.abs(transaction.amount)
    }
    acc[category].count += 1
    return acc
  }, {} as Record<string, { income: number; expenses: number; count: number }>)

  const topExpenseCategories = Object.entries(categoryAnalysis)
    .sort(([,a], [,b]) => b.expenses - a.expenses)
    .slice(0, 5)

  const insights = [
    {
      title: 'توفير شهري ممتاز',
      description: `تمكنت من توفير ${netIncome.toLocaleString()} ر.س هذا الشهر`,
      type: 'positive',
      icon: TrendingUpIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50'
    },
    {
      title: 'تحكم في المصروفات',
      description: 'حافظ على معدل الإنفاق الحالي لتحقيق أهدافك',
      type: 'info',
      icon: ChartBarIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50'
    }
  ]

  return (
    <div className="space-y-8">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">التحليلات المتقدمة</h1>
          <p className="mt-1 text-sm text-gray-600">
            تحليل شامل لوضعك المالي واتجاهات الإنفاق
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3 space-x-reverse">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="input-field"
          >
            <option value="week">هذا الأسبوع</option>
            <option value="month">هذا الشهر</option>
            <option value="quarter">هذا الربع</option>
            <option value="year">هذا العام</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الدخل</p>
              <p className="text-2xl font-bold text-green-600">{totalIncome.toLocaleString()} ر.س</p>
            </div>
            <div className="w-12 h-12 bg-green-50 rounded-lg flex items-center justify-center">
              <TrendingUpIcon className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
              <p className="text-2xl font-bold text-red-600">{totalExpenses.toLocaleString()} ر.س</p>
            </div>
            <div className="w-12 h-12 bg-red-50 rounded-lg flex items-center justify-center">
              <TrendingDownIcon className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">صافي التوفير</p>
              <p className="text-2xl font-bold text-blue-600">{netIncome.toLocaleString()} ر.س</p>
            </div>
            <div className="w-12 h-12 bg-blue-50 rounded-lg flex items-center justify-center">
              <CurrencyDollarIcon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">عدد المعاملات</p>
              <p className="text-2xl font-bold text-gray-900">{transactions.length}</p>
            </div>
            <div className="w-12 h-12 bg-gray-50 rounded-lg flex items-center justify-center">
              <DocumentChartBarIcon className="w-6 h-6 text-gray-600" />
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">الإنفاق حسب الفئة</h2>
          <div className="space-y-4">
            {topExpenseCategories.map(([category, data], index) => {
              const percentage = totalExpenses > 0 ? (data.expenses / totalExpenses) * 100 : 0
              return (
                <div key={category} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="w-3 h-3 rounded-full bg-primary-500"></div>
                    <span className="text-sm font-medium text-gray-900">{category}</span>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-semibold text-gray-900">{data.expenses.toLocaleString()} ر.س</p>
                    <p className="text-xs text-gray-500">{percentage.toFixed(1)}%</p>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">رؤى مالية ذكية</h2>
          <div className="space-y-4">
            {insights.map((insight, index) => {
              const IconComponent = insight.icon
              return (
                <div key={index} className={`${insight.bgColor} p-4 rounded-lg border border-gray-200`}>
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <div className={`w-10 h-10 ${insight.bgColor} rounded-lg flex items-center justify-center`}>
                      <IconComponent className={`w-5 h-5 ${insight.color}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-medium text-gray-900 mb-1">{insight.title}</h3>
                      <p className="text-sm text-gray-600">{insight.description}</p>
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>
      </div>
    </div>
  )
}