'use client'

import Link from 'next/link'
import { useData } from '@/contexts/DataContext'
import ClientOnly from '@/components/ClientOnly'
import ProtectedRoute from '@/components/ProtectedRoute'
import WeatherWidget from '@/components/WeatherWidget'
import QuickActions from '@/components/QuickActions'
import SmartReminders from '@/components/SmartReminders'
import MartyrTribute from '@/components/MartyrTribute'
import { getCurrentSession } from '@/lib/auth'
import {
  CurrencyDollarIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  PlusCircleIcon,
  ChartBarIcon,
  BellIcon,
  SparklesIcon,
  RocketLaunchIcon,
  BoltIcon,
  TrophyIcon
} from '@heroicons/react/24/outline'

// تم نقل هذا داخل المكون



export default function DashboardPage() {
  return (
    <ProtectedRoute>
      <DashboardContent />
    </ProtectedRoute>
  )
}

function DashboardContent() {
  const { userData, isLoading } = useData()
  
  // حساب الإحصائيات من البيانات المحفوظة
  const transactions = userData?.transactions || []
  const totalIncome = transactions.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0)
  const totalExpenses = transactions.filter(t => t.amount < 0).reduce((sum, t) => sum + Math.abs(t.amount), 0)
  const balance = totalIncome - totalExpenses
  
  const userName = userData?.profile && userData.profile.firstName && userData.profile.lastName 
    ? `${userData.profile.firstName} ${userData.profile.lastName}` 
    : 'مرحباً بك'
  
  // إحصائيات سريعة خارقة من البيانات الحقيقية
  const quickStats = [
    {
      title: 'الرصيد الحالي',
      value: balance.toString(),
      unit: 'ر.س',
      change: balance > 0 ? '+' : '',
      changeType: balance > 0 ? 'increase' : 'decrease',
      icon: CurrencyDollarIcon,
      color: balance > 0 ? 'neon' : 'cyber',
      gradient: balance > 0 ? 'from-neon-400 to-neon-600' : 'from-cyber-400 to-cyber-600',
      bgGradient: balance > 0 ? 'from-neon-50 to-neon-100' : 'from-cyber-50 to-cyber-100',
      shadow: balance > 0 ? 'shadow-neon' : 'shadow-cyber'
    },
    {
      title: 'إجمالي الدخل',
      value: totalIncome.toString(),
      unit: 'ر.س',
      change: '+' + totalIncome,
      changeType: 'increase',
      icon: ArrowUpIcon,
      color: 'electric',
      gradient: 'from-electric-400 to-electric-600',
      bgGradient: 'from-electric-50 to-electric-100',
      shadow: 'shadow-glow'
    },
    {
      title: 'إجمالي المصروفات',
      value: totalExpenses.toString(),
      unit: 'ر.س',
      change: '-' + totalExpenses,
      changeType: 'decrease',
      icon: ArrowDownIcon,
      color: 'gold',
      gradient: 'from-gold-400 to-gold-600',
      bgGradient: 'from-gold-50 to-gold-100',
      shadow: 'shadow-glow-lg'
    },
    {
      title: 'عدد المعاملات',
      value: transactions.length.toString(),
      unit: 'معاملة',
      change: '+' + transactions.length,
      changeType: 'increase',
      icon: TrophyIcon,
      color: 'primary',
      gradient: 'from-primary-400 to-primary-600',
      bgGradient: 'from-primary-50 to-primary-100',
      shadow: 'shadow-glow'
    }
  ]
  
  // المعاملات الحديثة من البيانات المحفوظة (آخر 5 معاملات)
  const recentTransactions = transactions.length > 0 ? transactions
    .slice(-5)
    .reverse()
    .map(t => ({
      id: t.id,
      type: t.title,
      description: t.description || t.category,
      amount: t.amount,
      date: t.date,
      status: t.status === 'completed' ? 'مكتمل' : t.status === 'pending' ? 'معلق' : 'فاشل',
      statusColor: t.status === 'completed' ? 'text-green-600 bg-green-50' : 
                   t.status === 'pending' ? 'text-yellow-600 bg-yellow-50' : 
                   'text-red-600 bg-red-50'
    })) : []
  
  return (
    <div className="space-y-8 min-h-screen bg-gradient-to-br from-gray-50 via-white to-electric-50">
      {/* إشارة للشهيد */}
      <MartyrTribute variant="header" className="mb-6 animate-fade-in" />

      {/* قسم الترحيب الخارق */}
      <div className="relative overflow-hidden bg-gradient-to-br from-electric-600 via-cyber-600 to-neon-600 rounded-3xl p-8 text-white shadow-glow-lg">
        {/* خلفية متحركة */}
        <div className="absolute inset-0 bg-hero-pattern opacity-30"></div>
        <div className="absolute inset-0 bg-cyber-grid bg-grid opacity-20"></div>

        <div className="relative flex items-center justify-between">
          <div className="flex-1">
            <div className="flex items-center space-x-3 space-x-reverse mb-4">
              <div className="w-12 h-12 rounded-full bg-gradient-to-r from-gold-400 to-gold-600 flex items-center justify-center animate-float">
                <SparklesIcon className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold mb-1 animate-slide-up">👋 أهلاً بك {userName}</h1>
                <p className="text-electric-100 animate-fade-in">مرحباً بك في لوحة التحكم المالية الخارقة</p>
              </div>
            </div>
            <div className="flex items-center space-x-4 space-x-reverse">
              <div className="flex items-center space-x-2 space-x-reverse bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 animate-glow">
                <RocketLaunchIcon className="w-5 h-5 text-gold-300" />
                <span className="text-sm font-medium">تقنيات متطورة</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse bg-white/20 backdrop-blur-sm rounded-full px-4 py-2 animate-glow">
                <BoltIcon className="w-5 h-5 text-neon-300" />
                <span className="text-sm font-medium">أداء فائق</span>
              </div>
            </div>
          </div>
          <div className="hidden md:block text-right animate-slide-down">
            <div className="bg-white/20 backdrop-blur-sm rounded-2xl p-4 border border-white/30">
              <p className="text-sm text-electric-200 mb-1">اليوم</p>
              <ClientOnly>
                <p className="text-xl font-bold text-white">{new Date().toLocaleDateString('ar-SA')}</p>
              </ClientOnly>
              <div className="flex items-center justify-end mt-2 space-x-1 space-x-reverse">
                <div className="w-2 h-2 bg-neon-400 rounded-full animate-pulse"></div>
                <span className="text-xs text-electric-200">متصل الآن</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* إحصائيات خارقة */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {quickStats.map((stat, index) => {
          const IconComponent = stat.icon
          return (
            <div
              key={index}
              className={`relative p-6 rounded-2xl bg-gradient-to-br ${stat.bgGradient} border-2 border-${stat.color}-200 ${stat.shadow} hover:scale-105 transition-all duration-500 animate-fade-in group overflow-hidden`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* خلفية متحركة */}
              <div className="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

              <div className="relative flex items-center justify-between">
                <div className="flex-1">
                  <p className={`text-sm font-medium text-${stat.color}-700 mb-2`}>{stat.title}</p>
                  <p className={`text-3xl font-bold text-${stat.color}-800 mb-3 group-hover:animate-pulse`}>
                    {stat.value}
                    <span className={`text-sm font-normal text-${stat.color}-600 mr-2`}>{stat.unit}</span>
                  </p>
                  <div className="flex items-center">
                    {stat.changeType === 'increase' ? (
                      <ArrowUpIcon className={`w-4 h-4 text-${stat.color}-600 ml-1 animate-bounce-slow`} />
                    ) : (
                      <ArrowDownIcon className={`w-4 h-4 text-${stat.color}-600 ml-1 animate-bounce-slow`} />
                    )}
                    <span className={`text-sm font-medium text-${stat.color}-700`}>
                      {stat.change}
                    </span>
                    <span className={`text-xs text-${stat.color}-600 mr-2`}>هذا الشهر</span>
                  </div>
                </div>
                <div className={`w-16 h-16 rounded-2xl bg-gradient-to-r ${stat.gradient} flex items-center justify-center group-hover:animate-float shadow-lg`}>
                  <IconComponent className="w-8 h-8 text-white" />
                </div>
              </div>

              {/* تأثير الوهج */}
              <div className={`absolute -inset-1 bg-gradient-to-r ${stat.gradient} rounded-2xl blur opacity-20 group-hover:opacity-40 transition-opacity duration-300 -z-10`}></div>
            </div>
          )
        })}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
        {/* Recent Transactions */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-900">آخر العمليات</h2>
                <Link 
                  href="/dashboard/history" 
                  className="text-primary-600 hover:text-primary-700 text-sm font-medium flex items-center space-x-1 space-x-reverse"
                >
                  <span>عرض الكل</span>
                  <EyeIcon className="w-4 h-4" />
                </Link>
              </div>
            </div>
            {recentTransactions.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="px-6 py-4 hover:bg-gray-50 transition-colors">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h3 className="text-sm font-medium text-gray-900">{transaction.type}</h3>
                          <div className="text-left">
                            {transaction.amount !== 0 && (
                              <p className={`text-sm font-semibold ${transaction.amount > 0 ? 'text-green-600' : 'text-red-600'}`}>
                                {transaction.amount > 0 ? '+' : ''}{transaction.amount} ر.س
                              </p>
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{transaction.description}</p>
                        <div className="flex items-center justify-between mt-2">
                          <p className="text-xs text-gray-500">{transaction.date}</p>
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${transaction.statusColor}`}>
                            {transaction.status}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="px-6 py-12 text-center">
                <DocumentTextIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">مرحباً بك في منصتنا!</h3>
                <p className="text-gray-500 mb-4">
                  لم تقم بإجراء أي معاملات بعد. ابدأ بإضافة معاملتك الأولى!
                </p>
                <Link href="/dashboard/history" className="btn-primary">
                  إضافة معاملة جديدة
                </Link>
              </div>
            )}
          </div>
        </div>

        {/* Weather & Market Widget */}
        <div className="lg:col-span-1">
          <WeatherWidget />
        </div>
        
        {/* Quick Actions */}
        <div className="lg:col-span-1">
          <QuickActions />
        </div>
      </div>

      {/* Smart Reminders */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <SmartReminders />
        
        {/* Additional Info Card */}
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-6 border border-green-200">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">💰 نصائح مالية</h2>
          <div className="space-y-3">
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">احرص على توفير 20% من دخلك الشهري</p>
            </div>
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">راجع فواتيرك بانتظام لتجنب المفاجآت</p>
            </div>
            <div className="flex items-start space-x-3 space-x-reverse">
              <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">استخدم التطبيق يومياً لتتبع أفضل</p>
            </div>
          </div>
        </div>
      </div>

      {/* Additional Quick Links */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">روابط سريعة</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link 
            href="/dashboard/requests" 
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group cursor-pointer"
          >
            <PlusCircleIcon className="w-8 h-8 text-primary-600 ml-3 group-hover:scale-110 transition-transform" />
            <div>
              <h3 className="font-medium text-gray-900 group-hover:text-primary-700">تقديم طلب جديد</h3>
              <p className="text-sm text-gray-600">طلب سلفة أو خدمة مالية</p>
            </div>
          </Link>
          
          <Link 
            href="/dashboard/documents" 
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group cursor-pointer"
          >
            <DocumentTextIcon className="w-8 h-8 text-primary-600 ml-3 group-hover:scale-110 transition-transform" />
            <div>
              <h3 className="font-medium text-gray-900 group-hover:text-primary-700">رفع مستند</h3>
              <p className="text-sm text-gray-600">إضافة فاتورة أو وثيقة</p>
            </div>
          </Link>
          
          <Link 
            href="/dashboard/reports" 
            className="flex items-center p-4 border border-gray-200 rounded-lg hover:border-primary-300 hover:bg-primary-50 transition-all duration-200 group cursor-pointer"
          >
            <ChartBarIcon className="w-8 h-8 text-primary-600 ml-3 group-hover:scale-110 transition-transform" />
            <div>
              <h3 className="font-medium text-gray-900 group-hover:text-primary-700">عرض التقارير</h3>
              <p className="text-sm text-gray-600">تحليل الإنفاق والدخل</p>
            </div>
          </Link>
        </div>
      </div>
    </div>
  )
}