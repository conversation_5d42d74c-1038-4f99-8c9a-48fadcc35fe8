'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import {
  DocumentTextIcon,
  BoltIcon,
  HomeIcon,
  PhoneIcon,
  TruckIcon,
  CreditCardIcon,
  BuildingOfficeIcon
} from '@heroicons/react/24/outline'

interface InvoiceTemplate {
  id: string
  name: string
  icon: React.ComponentType<any>
  color: string
  bgColor: string
  category: string
  defaultAmount?: number
  company: string
  description: string
}

export default function InvoiceTemplates() {
  const { addInvoice } = useData()
  const { success } = useToastContext()

  const templates: InvoiceTemplate[] = [
    {
      id: 'electricity',
      name: 'فاتورة كهرباء',
      icon: BoltIcon,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      category: 'مرافق',
      company: 'الشركة السعودية للكهرباء',
      description: 'فاتورة استهلاك الكهرباء الشهرية'
    },
    {
      id: 'water',
      name: 'فاتورة مياه',
      icon: HomeIcon,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      category: 'مرافق',
      company: 'شركة المياه الوطنية',
      description: 'فاتورة استهلاك المياه الشهرية'
    },
    {
      id: 'internet',
      name: 'فاتورة إنترنت',
      icon: PhoneIcon,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      category: 'اتصالات',
      defaultAmount: 150,
      company: 'شركة الاتصالات السعودية',
      description: 'فاتورة خدمة الإنترنت الشهرية'
    },
    {
      id: 'gas',
      name: 'فاتورة غاز',
      icon: TruckIcon,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      category: 'مرافق',
      company: 'شركة الغاز والتصنيع',
      description: 'فاتورة استهلاك الغاز الشهرية'
    },
    {
      id: 'credit_card',
      name: 'فاتورة بطاقة ائتمان',
      icon: CreditCardIcon,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      category: 'مصرفية',
      company: 'البنك الأهلي السعودي',
      description: 'كشف حساب البطاقة الائتمانية'
    },
    {
      id: 'rent',
      name: 'فاتورة إيجار',
      icon: BuildingOfficeIcon,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      category: 'سكن',
      defaultAmount: 2000,
      company: 'مكتب العقارات',
      description: 'فاتورة إيجار شهرية'
    }
  ]

  const handleCreateFromTemplate = (template: InvoiceTemplate) => {
    const amount = template.defaultAmount || 
      Number(prompt(`أدخل مبلغ ${template.name}:`)) || 100

    if (amount > 0) {
      const newInvoice = {
        title: template.name,
        company: template.company,
        amount: amount,
        dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        status: 'pending' as const,
        category: template.category,
        description: template.description,
        attachments: [],
        createdAt: new Date().toISOString()
      }
      
      addInvoice(newInvoice)
      success(`تم إنشاء ${template.name} بمبلغ ${amount} ر.س`)
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <h2 className="text-lg font-semibold text-gray-900 mb-4">قوالب الفواتير السريعة</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {templates.map((template) => {
          const IconComponent = template.icon
          return (
            <button
              key={template.id}
              onClick={() => handleCreateFromTemplate(template)}
              className={`${template.bgColor} p-4 rounded-lg border border-gray-200 hover:shadow-md transition-all duration-200 group text-right`}
            >
              <div className="flex items-center space-x-3 space-x-reverse">
                <div className={`w-10 h-10 ${template.bgColor} rounded-lg flex items-center justify-center group-hover:scale-110 transition-transform`}>
                  <IconComponent className={`w-5 h-5 ${template.color}`} />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900">{template.name}</h3>
                  <p className="text-sm text-gray-600">{template.company}</p>
                  <p className="text-xs text-gray-500 mt-1">{template.category}</p>
                </div>
              </div>
            </button>
          )
        })}
      </div>
    </div>
  )
}