'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import {
  PlusIcon,
  PencilIcon,
  TrashIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  CurrencyDollarIcon,
  CalendarIcon
} from '@heroicons/react/24/outline'

export default function BudgetPage() {
  const { userData } = useData()
  const { success } = useToastContext()
  const [budgets, setBudgets] = useState([
    { id: '1', category: 'طعام', allocated: 2000, spent: 1500, period: 'شهري' },
    { id: '2', category: 'مواصلات', allocated: 800, spent: 650, period: 'شهري' },
    { id: '3', category: 'ترفيه', allocated: 500, spent: 750, period: 'شهري' },
    { id: '4', category: 'ملابس', allocated: 1000, spent: 200, period: 'شهري' }
  ])

  const totalAllocated = budgets.reduce((sum, b) => sum + b.allocated, 0)
  const totalSpent = budgets.reduce((sum, b) => sum + b.spent, 0)

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الميزانية</h1>
          <p className="text-gray-600">خطط وتتبع ميزانيتك الشهرية</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          إضافة فئة جديدة
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المخصص</p>
              <p className="text-2xl font-bold text-blue-600">{totalAllocated.toLocaleString()} ر.س</p>
            </div>
            <ChartBarIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المصروف</p>
              <p className="text-2xl font-bold text-red-600">{totalSpent.toLocaleString()} ر.س</p>
            </div>
            <CurrencyDollarIcon className="w-8 h-8 text-red-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المتبقي</p>
              <p className={`text-2xl font-bold ${totalAllocated - totalSpent >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {(totalAllocated - totalSpent).toLocaleString()} ر.س
              </p>
            </div>
            {totalAllocated - totalSpent >= 0 ? 
              <CheckCircleIcon className="w-8 h-8 text-green-600" /> :
              <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
            }
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">فئات الميزانية</h2>
        </div>
        <div className="divide-y">
          {budgets.map((budget) => {
            const percentage = (budget.spent / budget.allocated) * 100
            const isOverBudget = budget.spent > budget.allocated
            
            return (
              <div key={budget.id} className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div>
                    <h3 className="font-medium text-gray-900">{budget.category}</h3>
                    <p className="text-sm text-gray-500">{budget.period}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <button className="p-2 text-gray-600 hover:bg-gray-100 rounded">
                      <PencilIcon className="w-4 h-4" />
                    </button>
                    <button className="p-2 text-red-600 hover:bg-red-50 rounded">
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>المصروف: {budget.spent.toLocaleString()} ر.س</span>
                    <span>المخصص: {budget.allocated.toLocaleString()} ر.س</span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${isOverBudget ? 'bg-red-500' : 'bg-green-500'}`}
                      style={{ width: `${Math.min(percentage, 100)}%` }}
                    ></div>
                  </div>
                  
                  <div className="flex justify-between text-xs text-gray-500">
                    <span>{percentage.toFixed(1)}% مستخدم</span>
                    <span className={isOverBudget ? 'text-red-600 font-medium' : ''}>
                      {isOverBudget ? `تجاوز بـ ${(budget.spent - budget.allocated).toLocaleString()} ر.س` : 
                       `متبقي ${(budget.allocated - budget.spent).toLocaleString()} ر.س`}
                    </span>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
      </div>
    </div>
  )
}