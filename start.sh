#!/bin/bash

echo "========================================"
echo "       تشغيل موقع متابعة مالية"
echo "========================================"
echo

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "خطأ: Node.js غير مثبت على النظام"
    echo "يرجى تثبيت Node.js من: https://nodejs.org"
    exit 1
fi

echo "تحقق من Node.js... ✓"
echo "الإصدار: $(node --version)"
echo

# Install dependencies
echo "تثبيت الحزم المطلوبة..."
npm install

if [ $? -ne 0 ]; then
    echo "خطأ في تثبيت الحزم"
    exit 1
fi

echo
echo "تشغيل الخادم المحلي..."
echo "يمكنك الوصول للموقع على: http://localhost:3000"
echo
echo "اضغط Ctrl+C لإيقاف الخادم"
echo

# Start the development server
npm run dev