'use client'

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { 
  UserData, 
  Transaction, 
  Invoice, 
  Request, 
  Document,
  getStoredData, 
  saveToStorage, 
  getDefaultUserData 
} from '@/lib/storage'

interface DataContextType {
  userData: UserData
  isLoading: boolean
  
  // Profile functions
  updateProfile: (profile: Partial<UserData['profile']>) => void
  
  // Transaction functions
  addTransaction: (transaction: Omit<Transaction, 'id'>) => void
  updateTransaction: (id: string, transaction: Partial<Transaction>) => void
  deleteTransaction: (id: string) => void
  
  // Invoice functions
  addInvoice: (invoice: Omit<Invoice, 'id'>) => void
  updateInvoice: (id: string, invoice: Partial<Invoice>) => void
  deleteInvoice: (id: string) => void
  
  // Request functions
  addRequest: (request: Omit<Request, 'id'>) => void
  updateRequest: (id: string, request: Partial<Request>) => void
  deleteRequest: (id: string) => void
  
  // Document functions
  addDocument: (document: Omit<Document, 'id'>) => void
  updateDocument: (id: string, document: Partial<Document>) => void
  deleteDocument: (id: string) => void
  
  // Settings functions
  updateSettings: (settings: Partial<UserData['settings']>) => void
  
  // Utility functions
  exportData: () => string
  importData: (jsonData: string) => boolean
  clearAllData: () => void
}

const DataContext = createContext<DataContextType | undefined>(undefined)

export const useData = () => {
  const context = useContext(DataContext)
  if (context === undefined) {
    throw new Error('useData must be used within a DataProvider')
  }
  return context
}

export const DataProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [userData, setUserData] = useState<UserData>(getDefaultUserData())
  const [isLoading, setIsLoading] = useState(true)

  // تحميل البيانات عند بدء التطبيق
  useEffect(() => {
    const loadData = () => {
      try {
        console.log('🔄 جاري تحميل البيانات المحفوظة...')
        
        const storedProfile = getStoredData('profile')
        const storedTransactions = getStoredData('transactions')
        const storedInvoices = getStoredData('invoices')
        const storedRequests = getStoredData('requests')
        const storedDocuments = getStoredData('documents')
        const storedSettings = getStoredData('settings')

        const defaultData = getDefaultUserData()

        const loadedData = {
          profile: storedProfile || defaultData.profile,
          transactions: storedTransactions || defaultData.transactions,
          invoices: storedInvoices || defaultData.invoices,
          requests: storedRequests || defaultData.requests,
          documents: storedDocuments || defaultData.documents,
          settings: storedSettings || defaultData.settings
        }

        setUserData(loadedData)
        
        console.log('✅ تم تحميل البيانات بنجاح:', {
          profile: !!storedProfile,
          transactions: storedTransactions?.length || 0,
          invoices: storedInvoices?.length || 0,
          requests: storedRequests?.length || 0,
          documents: storedDocuments?.length || 0,
          settings: !!storedSettings
        })
        
      } catch (error) {
        console.error('❌ خطأ في تحميل البيانات:', error)
        setUserData(getDefaultUserData())
      } finally {
        setIsLoading(false)
      }
    }

    loadData()
  }, [])

  // Profile functions
  const updateProfile = (newProfile: Partial<UserData['profile']>) => {
    const updatedProfile = { ...userData.profile, ...newProfile }
    setUserData(prev => ({ ...prev, profile: updatedProfile }))
    const saved = saveToStorage('profile', updatedProfile)
    console.log('✅ تم حفظ المعلومات الشخصية:', Object.keys(newProfile), 'حالة الحفظ:', saved)
  }

  // Transaction functions
  const addTransaction = (transaction: Omit<Transaction, 'id'>) => {
    const newTransaction: Transaction = {
      ...transaction,
      id: Date.now().toString()
    }
    const updatedTransactions = [...userData.transactions, newTransaction]
    setUserData(prev => ({ ...prev, transactions: updatedTransactions }))
    const saved = saveToStorage('transactions', updatedTransactions)
    console.log('✅ تم حفظ معاملة جديدة:', newTransaction.title, 'حالة الحفظ:', saved)
  }

  const updateTransaction = (id: string, updatedTransaction: Partial<Transaction>) => {
    const updatedTransactions = userData.transactions.map(t => 
      t.id === id ? { ...t, ...updatedTransaction } : t
    )
    setUserData(prev => ({ ...prev, transactions: updatedTransactions }))
    saveToStorage('transactions', updatedTransactions)
    console.log('✅ تم تحديث المعاملة تلقائياً')
  }

  const deleteTransaction = (id: string) => {
    const updatedTransactions = userData.transactions.filter(t => t.id !== id)
    setUserData(prev => ({ ...prev, transactions: updatedTransactions }))
    saveToStorage('transactions', updatedTransactions)
    console.log('✅ تم حذف المعاملة تلقائياً')
  }

  // Invoice functions
  const addInvoice = (invoice: Omit<Invoice, 'id'>) => {
    const newInvoice: Invoice = {
      ...invoice,
      id: Date.now().toString()
    }
    const updatedInvoices = [...userData.invoices, newInvoice]
    setUserData(prev => ({ ...prev, invoices: updatedInvoices }))
    saveToStorage('invoices', updatedInvoices)
  }

  const updateInvoice = (id: string, updatedInvoice: Partial<Invoice>) => {
    const updatedInvoices = userData.invoices.map(i => 
      i.id === id ? { ...i, ...updatedInvoice } : i
    )
    setUserData(prev => ({ ...prev, invoices: updatedInvoices }))
    saveToStorage('invoices', updatedInvoices)
  }

  const deleteInvoice = (id: string) => {
    const updatedInvoices = userData.invoices.filter(i => i.id !== id)
    setUserData(prev => ({ ...prev, invoices: updatedInvoices }))
    saveToStorage('invoices', updatedInvoices)
  }

  // Request functions
  const addRequest = (request: Omit<Request, 'id'>) => {
    const newRequest: Request = {
      ...request,
      id: Date.now().toString()
    }
    const updatedRequests = [...userData.requests, newRequest]
    setUserData(prev => ({ ...prev, requests: updatedRequests }))
    saveToStorage('requests', updatedRequests)
  }

  const updateRequest = (id: string, updatedRequest: Partial<Request>) => {
    const updatedRequests = userData.requests.map(r => 
      r.id === id ? { ...r, ...updatedRequest } : r
    )
    setUserData(prev => ({ ...prev, requests: updatedRequests }))
    saveToStorage('requests', updatedRequests)
  }

  const deleteRequest = (id: string) => {
    const updatedRequests = userData.requests.filter(r => r.id !== id)
    setUserData(prev => ({ ...prev, requests: updatedRequests }))
    saveToStorage('requests', updatedRequests)
  }

  // Document functions
  const addDocument = (document: Omit<Document, 'id'>) => {
    const newDocument: Document = {
      ...document,
      id: Date.now().toString()
    }
    const updatedDocuments = [...userData.documents, newDocument]
    setUserData(prev => ({ ...prev, documents: updatedDocuments }))
    saveToStorage('documents', updatedDocuments)
  }

  const updateDocument = (id: string, updatedDocument: Partial<Document>) => {
    const updatedDocuments = userData.documents.map(d => 
      d.id === id ? { ...d, ...updatedDocument } : d
    )
    setUserData(prev => ({ ...prev, documents: updatedDocuments }))
    saveToStorage('documents', updatedDocuments)
  }

  const deleteDocument = (id: string) => {
    const updatedDocuments = userData.documents.filter(d => d.id !== id)
    setUserData(prev => ({ ...prev, documents: updatedDocuments }))
    saveToStorage('documents', updatedDocuments)
  }

  // Settings functions
  const updateSettings = (newSettings: Partial<UserData['settings']>) => {
    const updatedSettings = { ...userData.settings, ...newSettings }
    setUserData(prev => ({ ...prev, settings: updatedSettings }))
    saveToStorage('settings', updatedSettings)
    console.log('✅ تم حفظ الإعدادات تلقائياً')
  }

  // Utility functions
  const exportData = () => {
    return JSON.stringify(userData, null, 2)
  }

  const importData = (jsonData: string) => {
    try {
      const importedData: UserData = JSON.parse(jsonData)
      setUserData(importedData)
      
      // حفظ جميع البيانات
      saveToStorage('profile', importedData.profile)
      saveToStorage('transactions', importedData.transactions)
      saveToStorage('invoices', importedData.invoices)
      saveToStorage('requests', importedData.requests)
      saveToStorage('documents', importedData.documents)
      saveToStorage('settings', importedData.settings)
      
      return true
    } catch (error) {
      console.error('Error importing data:', error)
      return false
    }
  }

  const clearAllData = () => {
    const defaultData = getDefaultUserData()
    setUserData(defaultData)
    
    // مسح جميع البيانات المحفوظة
    localStorage.removeItem('financialTracker_profile')
    localStorage.removeItem('financialTracker_transactions')
    localStorage.removeItem('financialTracker_invoices')
    localStorage.removeItem('financialTracker_requests')
    localStorage.removeItem('financialTracker_documents')
    localStorage.removeItem('financialTracker_settings')
  }

  const contextValue: DataContextType = {
    userData,
    isLoading,
    updateProfile,
    addTransaction,
    updateTransaction,
    deleteTransaction,
    addInvoice,
    updateInvoice,
    deleteInvoice,
    addRequest,
    updateRequest,
    deleteRequest,
    addDocument,
    updateDocument,
    deleteDocument,
    updateSettings,
    exportData,
    importData,
    clearAllData
  }

  return (
    <DataContext.Provider value={contextValue}>
      {children}
    </DataContext.Provider>
  )
}