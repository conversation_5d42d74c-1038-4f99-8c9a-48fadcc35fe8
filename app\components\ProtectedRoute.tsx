'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { getCurrentSession, isAuthenticated } from '@/lib/auth'
import LoadingSpinner from './LoadingSpinner'

interface ProtectedRouteProps {
  children: React.ReactNode
  requireAuth?: boolean
  redirectTo?: string
  allowedRoles?: ('user' | 'admin')[]
}

export default function ProtectedRoute({ 
  children, 
  requireAuth = true,
  redirectTo = '/auth/login',
  allowedRoles = ['user', 'admin']
}: ProtectedRouteProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasAccess, setHasAccess] = useState(false)
  const router = useRouter()

  useEffect(() => {
    const checkAuth = async () => {
      try {
        if (!requireAuth) {
          setHasAccess(true)
          setIsLoading(false)
          return
        }

        const authenticated = isAuthenticated()
        
        if (!authenticated) {
          router.push(redirectTo)
          return
        }

        const session = getCurrentSession()
        
        if (!session) {
          router.push(redirectTo)
          return
        }

        // التحقق من الأدوار المسموحة
        if (!allowedRoles.includes(session.user.role)) {
          router.push('/unauthorized')
          return
        }

        setHasAccess(true)
      } catch (error) {
        console.error('خطأ في التحقق من المصادقة:', error)
        router.push(redirectTo)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()
  }, [requireAuth, redirectTo, allowedRoles, router])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner />
      </div>
    )
  }

  if (!hasAccess) {
    return null
  }

  return <>{children}</>
}
