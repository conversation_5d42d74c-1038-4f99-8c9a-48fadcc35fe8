'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import { XMarkIcon } from '@heroicons/react/24/outline'

interface AddTransactionModalProps {
  isOpen: boolean
  onClose: () => void
}

export default function AddTransactionModal({ isOpen, onClose }: AddTransactionModalProps) {
  const { addTransaction } = useData()
  const { success, error } = useToastContext()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [formData, setFormData] = useState({
    type: 'payment' as 'payment' | 'income' | 'transfer' | 'refund',
    title: '',
    description: '',
    amount: '',
    category: '',
    paymentMethod: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().slice(0, 5)
  })

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      const amount = parseFloat(formData.amount)
      if (isNaN(amount) || amount === 0) {
        error('يرجى إدخال مبلغ صحيح')
        return
      }

      const transaction = {
        type: formData.type,
        title: formData.title,
        description: formData.description,
        amount: formData.type === 'payment' ? -Math.abs(amount) : Math.abs(amount),
        date: formData.date,
        time: formData.time,
        status: 'completed' as const,
        category: formData.category,
        reference: `TXN-${Date.now()}`,
        paymentMethod: formData.paymentMethod
      }

      addTransaction(transaction)
      success('تم إضافة المعاملة بنجاح! ✅')
      onClose()
      
      // Reset form
      setFormData({
        type: 'payment',
        title: '',
        description: '',
        amount: '',
        category: '',
        paymentMethod: '',
        date: new Date().toISOString().split('T')[0],
        time: new Date().toTimeString().slice(0, 5)
      })
    } catch (err) {
      error('حدث خطأ في إضافة المعاملة')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <div className="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" onClick={onClose}></div>

        <div className="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-right align-middle transition-all transform bg-white shadow-xl rounded-lg">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-medium text-gray-900">إضافة معاملة جديدة</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                نوع المعاملة
              </label>
              <select
                value={formData.type}
                onChange={(e) => setFormData({...formData, type: e.target.value as any})}
                className="input-field"
                required
              >
                <option value="payment">دفع</option>
                <option value="income">دخل</option>
                <option value="transfer">تحويل</option>
                <option value="refund">استرداد</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                عنوان المعاملة
              </label>
              <input
                type="text"
                value={formData.title}
                onChange={(e) => setFormData({...formData, title: e.target.value})}
                className="input-field"
                placeholder="مثال: دفع فاتورة الكهرباء"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                الوصف
              </label>
              <textarea
                value={formData.description}
                onChange={(e) => setFormData({...formData, description: e.target.value})}
                className="input-field"
                rows={2}
                placeholder="تفاصيل إضافية عن المعاملة"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  المبلغ (ر.س)
                </label>
                <input
                  type="number"
                  step="0.01"
                  value={formData.amount}
                  onChange={(e) => setFormData({...formData, amount: e.target.value})}
                  className="input-field"
                  placeholder="0.00"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الفئة
                </label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                  className="input-field"
                  required
                >
                  <option value="">اختر الفئة</option>
                  <option value="فواتير">فواتير</option>
                  <option value="طعام">طعام</option>
                  <option value="مواصلات">مواصلات</option>
                  <option value="ترفيه">ترفيه</option>
                  <option value="صحة">صحة</option>
                  <option value="راتب">راتب</option>
                  <option value="أخرى">أخرى</option>
                </select>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                طريقة الدفع
              </label>
              <select
                value={formData.paymentMethod}
                onChange={(e) => setFormData({...formData, paymentMethod: e.target.value})}
                className="input-field"
                required
              >
                <option value="">اختر طريقة الدفع</option>
                <option value="نقد">نقد</option>
                <option value="بطاقة ائتمان">بطاقة ائتمان</option>
                <option value="بطاقة مدين">بطاقة مدين</option>
                <option value="تحويل بنكي">تحويل بنكي</option>
                <option value="محفظة إلكترونية">محفظة إلكترونية</option>
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  التاريخ
                </label>
                <input
                  type="date"
                  value={formData.date}
                  onChange={(e) => setFormData({...formData, date: e.target.value})}
                  className="input-field"
                  required
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  الوقت
                </label>
                <input
                  type="time"
                  value={formData.time}
                  onChange={(e) => setFormData({...formData, time: e.target.value})}
                  className="input-field"
                  required
                />
              </div>
            </div>

            <div className="flex space-x-3 space-x-reverse pt-4">
              <button
                type="submit"
                disabled={isSubmitting}
                className={`btn-primary flex-1 ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`}
              >
                {isSubmitting ? 'جاري الحفظ...' : 'حفظ المعاملة'}
              </button>
              <button
                type="button"
                onClick={onClose}
                className="btn-secondary flex-1"
              >
                إلغاء
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}