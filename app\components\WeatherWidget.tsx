'use client'

import { useState, useEffect } from 'react'
import { 
  SunIcon, 
  CloudIcon, 
  MapPinIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface WeatherData {
  city: string
  temperature: number
  condition: string
  icon: string
}

interface MarketData {
  name: string
  price: number
  change: number
  changePercent: number
}

export default function WeatherWidget() {
  const [weather, setWeather] = useState<WeatherData | null>(null)
  const [markets, setMarkets] = useState<MarketData[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // محاكاة بيانات الطقس
    setTimeout(() => {
      setWeather({
        city: 'الرياض',
        temperature: 28,
        condition: 'مشمس',
        icon: 'sunny'
      })

      setMarkets([
        { name: 'تاسي', price: 11250, change: 125, changePercent: 1.12 },
        { name: 'أرامكو', price: 32.5, change: -0.5, changePercent: -1.52 },
        { name: 'الراجحي', price: 85.2, change: 2.1, changePercent: 2.53 },
        { name: 'USD/SAR', price: 3.75, change: 0.01, changePercent: 0.27 }
      ])

      setLoading(false)
    }, 1000)
  }, [])

  const getWeatherIcon = (condition: string) => {
    switch (condition) {
      case 'مشمس':
        return <SunIcon className="w-8 h-8 text-yellow-500" />
      case 'غائم':
        return <CloudIcon className="w-8 h-8 text-gray-500" />
      default:
        return <SunIcon className="w-8 h-8 text-yellow-500" />
    }
  }

  if (loading) {
    return (
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-8 bg-gray-200 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/3"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Weather Card */}
      {weather && (
        <div className="bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <div className="flex items-center space-x-2 space-x-reverse mb-2">
                <MapPinIcon className="w-4 h-4" />
                <span className="text-sm opacity-90">{weather.city}</span>
              </div>
              <div className="text-3xl font-bold mb-1">
                {weather.temperature}°
              </div>
              <div className="text-sm opacity-90">
                {weather.condition}
              </div>
            </div>
            <div className="text-right">
              {getWeatherIcon(weather.condition)}
            </div>
          </div>
        </div>
      )}

      {/* Market Data */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <div className="flex items-center space-x-2 space-x-reverse mb-4">
          <CurrencyDollarIcon className="w-5 h-5 text-green-600" />
          <h3 className="text-lg font-semibold text-gray-900">الأسواق المالية</h3>
        </div>
        
        <div className="space-y-3">
          {markets.map((market, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <div className="font-medium text-gray-900">{market.name}</div>
                <div className="text-sm text-gray-600">{market.price}</div>
              </div>
              <div className="text-right">
                <div className={`flex items-center space-x-1 space-x-reverse text-sm ${
                  market.change >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {market.change >= 0 ? (
                    <ArrowTrendingUpIcon className="w-4 h-4" />
                  ) : (
                    <ArrowTrendingDownIcon className="w-4 h-4" />
                  )}
                  <span>{market.change >= 0 ? '+' : ''}{market.change}</span>
                </div>
                <div className={`text-xs ${
                  market.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
                }`}>
                  {market.changePercent >= 0 ? '+' : ''}{market.changePercent}%
                </div>
              </div>
            </div>
          ))}
        </div>
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            آخر تحديث: {new Date().toLocaleTimeString('ar-SA')}
          </p>
        </div>
      </div>
    </div>
  )
}