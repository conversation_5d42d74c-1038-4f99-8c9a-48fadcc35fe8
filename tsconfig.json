{"compilerOptions": {"target": "es2015", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/*": ["./app/*"], "@/app/*": ["./app/*"], "@/components/*": ["./app/components/*"], "@/contexts/*": ["./app/contexts/*"], "@/hooks/*": ["./app/hooks/*"], "@/lib/*": ["./app/lib/*"]}, "plugins": [{"name": "next"}]}, "include": ["**/*.ts", "**/*.tsx", ".next/types/**/*.ts", "next-env.d.ts", "out/types/**/*.ts"], "exclude": ["node_modules"]}