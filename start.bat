@echo off
echo ========================================
echo      تشغيل موقع متابعة مالية
echo ========================================
echo.

echo تحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo خطأ: Node.js غير مثبت على النظام
    echo يرجى تثبيت Node.js من: https://nodejs.org
    pause
    exit /b 1
)

echo تثبيت الحزم المطلوبة...
call npm install

if %errorlevel% neq 0 (
    echo خطأ في تثبيت الحزم
    pause
    exit /b 1
)

echo.
echo تشغيل الخادم المحلي...
echo يمكنك الوصول للموقع على: http://localhost:3000
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

call npm run dev

pause