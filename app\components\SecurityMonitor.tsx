'use client'

import { useState, useEffect } from 'react'
import { 
  ShieldExclamationIcon, 
  EyeIcon, 
  ClockIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline'
import { logSecurityEvent, detectSuspiciousActivity } from '@/lib/security'
import { getCurrentSession } from '@/lib/auth'

interface SecurityLog {
  id: string
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'suspicious_activity' | 'data_access'
  userId?: string
  timestamp: string
  ip?: string
  userAgent?: string
  details?: any
  severity: 'low' | 'medium' | 'high' | 'critical'
}

export default function SecurityMonitor() {
  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([])
  const [isVisible, setIsVisible] = useState(false)
  const [alertCount, setAlertCount] = useState(0)

  useEffect(() => {
    loadSecurityLogs()
    
    // مراقبة الأنشطة كل 30 ثانية
    const interval = setInterval(() => {
      monitorActivity()
    }, 30000)

    // مراقبة تغييرات التخزين المحلي
    const handleStorageChange = (e: StorageEvent) => {
      if (e.key?.startsWith('financialTracker_')) {
        logDataAccess(e.key, e.newValue ? 'write' : 'delete')
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      clearInterval(interval)
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  const loadSecurityLogs = () => {
    try {
      const logs = JSON.parse(localStorage.getItem('security_logs') || '[]')
      const processedLogs = logs.map((log: any) => ({
        ...log,
        severity: determineSeverity(log)
      }))
      setSecurityLogs(processedLogs)
      
      // حساب التنبيهات الجديدة (آخر ساعة)
      const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000)
      const recentAlerts = processedLogs.filter((log: SecurityLog) => 
        new Date(log.timestamp) > oneHourAgo && 
        (log.severity === 'high' || log.severity === 'critical')
      )
      setAlertCount(recentAlerts.length)
    } catch (error) {
      console.error('خطأ في تحميل سجلات الأمان:', error)
    }
  }

  const monitorActivity = () => {
    const session = getCurrentSession()
    if (!session) return

    // فحص الأنشطة المشبوهة
    const suspiciousActivity = detectSuspiciousActivity({
      type: 'routine_check',
      timestamp: new Date()
    })

    if (suspiciousActivity) {
      logSecurityEvent({
        type: 'suspicious_activity',
        userId: session.user.id,
        details: { 
          reason: 'automated_detection',
          checkTime: new Date().toISOString()
        }
      })
      loadSecurityLogs()
    }
  }

  const logDataAccess = (key: string, action: 'read' | 'write' | 'delete') => {
    const session = getCurrentSession()
    logSecurityEvent({
      type: 'data_access' as any,
      userId: session?.user.id,
      details: {
        dataKey: key,
        action,
        timestamp: new Date().toISOString()
      }
    })
  }

  const determineSeverity = (log: any): 'low' | 'medium' | 'high' | 'critical' => {
    switch (log.type) {
      case 'failed_login':
        return 'medium'
      case 'suspicious_activity':
        return 'high'
      case 'password_change':
        return 'medium'
      case 'login':
      case 'logout':
        return 'low'
      case 'data_access':
        return 'low'
      default:
        return 'low'
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'login':
        return <CheckCircleIcon className="w-4 h-4 text-green-500" />
      case 'logout':
        return <XCircleIcon className="w-4 h-4 text-gray-500" />
      case 'failed_login':
        return <ExclamationTriangleIcon className="w-4 h-4 text-yellow-500" />
      case 'suspicious_activity':
        return <ShieldExclamationIcon className="w-4 h-4 text-red-500" />
      case 'password_change':
        return <ClockIcon className="w-4 h-4 text-blue-500" />
      default:
        return <EyeIcon className="w-4 h-4 text-gray-400" />
    }
  }

  const getTypeLabel = (type: string) => {
    const labels: Record<string, string> = {
      login: 'تسجيل دخول',
      logout: 'تسجيل خروج',
      failed_login: 'فشل تسجيل دخول',
      suspicious_activity: 'نشاط مشبوه',
      password_change: 'تغيير كلمة المرور',
      data_access: 'الوصول للبيانات'
    }
    return labels[type] || type
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical':
        return 'text-red-600 bg-red-50'
      case 'high':
        return 'text-orange-600 bg-orange-50'
      case 'medium':
        return 'text-yellow-600 bg-yellow-50'
      case 'low':
        return 'text-green-600 bg-green-50'
      default:
        return 'text-gray-600 bg-gray-50'
    }
  }

  const clearLogs = () => {
    localStorage.removeItem('security_logs')
    setSecurityLogs([])
    setAlertCount(0)
  }

  return (
    <>
      {/* زر مراقب الأمان */}
      <button
        onClick={() => setIsVisible(!isVisible)}
        className="fixed bottom-4 left-4 bg-primary-600 text-white p-3 rounded-full shadow-lg hover:bg-primary-700 transition-colors z-40"
        title="مراقب الأمان"
      >
        <ShieldExclamationIcon className="w-6 h-6" />
        {alertCount > 0 && (
          <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {alertCount}
          </span>
        )}
      </button>

      {/* نافذة مراقب الأمان */}
      {isVisible && (
        <div className="fixed bottom-20 left-4 bg-white rounded-lg shadow-xl border w-96 max-h-96 overflow-hidden z-50">
          <div className="p-4 border-b bg-gray-50">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900 flex items-center">
                <ShieldExclamationIcon className="w-5 h-5 ml-2" />
                مراقب الأمان
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={clearLogs}
                  className="text-sm text-red-600 hover:text-red-800"
                >
                  مسح السجلات
                </button>
                <button
                  onClick={() => setIsVisible(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ✕
                </button>
              </div>
            </div>
          </div>

          <div className="overflow-y-auto max-h-80">
            {securityLogs.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                لا توجد سجلات أمنية
              </div>
            ) : (
              <div className="divide-y">
                {securityLogs.slice(-20).reverse().map((log) => (
                  <div key={log.id} className="p-3 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-3">
                        {getTypeIcon(log.type)}
                        <div className="flex-1">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium text-gray-900">
                              {getTypeLabel(log.type)}
                            </span>
                            <span className={`text-xs px-2 py-1 rounded-full ${getSeverityColor(log.severity)}`}>
                              {log.severity}
                            </span>
                          </div>
                          <p className="text-xs text-gray-500 mt-1">
                            {new Date(log.timestamp).toLocaleString('ar-SA')}
                          </p>
                          {log.details && (
                            <p className="text-xs text-gray-600 mt-1">
                              {JSON.stringify(log.details, null, 2)}
                            </p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </>
  )
}
