/**
 * نظام النسخ الاحتياطي الآمن
 * يتعامل مع إنشاء واستعادة النسخ الاحتياطية المشفرة
 */

import { encryptData, decryptData, logSecurityEvent } from './security'
import { getCurrentSession } from './auth'

export interface BackupData {
  version: string
  timestamp: string
  userId: string
  userData: any
  checksum: string
}

export interface BackupMetadata {
  id: string
  name: string
  timestamp: string
  size: number
  encrypted: boolean
  version: string
}

// إنشاء نسخة احتياطية آمنة
export const createSecureBackup = async (customName?: string): Promise<{
  success: boolean
  message: string
  backupData?: string
  metadata?: BackupMetadata
}> => {
  try {
    const session = getCurrentSession()
    if (!session) {
      return { success: false, message: 'يجب تسجيل الدخول أولاً' }
    }

    // جمع جميع البيانات
    const userData = {
      profile: getStoredData('profile'),
      transactions: getStoredData('transactions') || [],
      invoices: getStoredData('invoices') || [],
      requests: getStoredData('requests') || [],
      documents: getStoredData('documents') || [],
      settings: getStoredData('settings') || {}
    }

    // إنشاء checksum للتحقق من سلامة البيانات
    const dataString = JSON.stringify(userData)
    const checksum = await generateChecksum(dataString)

    // إنشاء بيانات النسخة الاحتياطية
    const backupData: BackupData = {
      version: '1.0.0',
      timestamp: new Date().toISOString(),
      userId: session.user.id,
      userData,
      checksum
    }

    // تشفير البيانات
    const encryptedBackup = encryptData(backupData)

    // إنشاء metadata
    const backupId = generateBackupId()
    const backupName = customName || `نسخة احتياطية ${new Date().toLocaleDateString('ar-SA')}`
    
    const metadata: BackupMetadata = {
      id: backupId,
      name: backupName,
      timestamp: backupData.timestamp,
      size: encryptedBackup.length,
      encrypted: true,
      version: backupData.version
    }

    // حفظ metadata في التخزين المحلي
    const existingBackups = getStoredBackups()
    existingBackups.push(metadata)
    localStorage.setItem('financial_tracker_backups', JSON.stringify(existingBackups))

    // تسجيل العملية
    logSecurityEvent({
      type: 'data_access' as any,
      userId: session.user.id,
      details: {
        action: 'backup_created',
        backupId,
        backupName,
        dataSize: encryptedBackup.length
      }
    })

    return {
      success: true,
      message: 'تم إنشاء النسخة الاحتياطية بنجاح',
      backupData: encryptedBackup,
      metadata
    }

  } catch (error) {
    console.error('خطأ في إنشاء النسخة الاحتياطية:', error)
    return { success: false, message: 'فشل في إنشاء النسخة الاحتياطية' }
  }
}

// استعادة النسخة الاحتياطية
export const restoreSecureBackup = async (encryptedBackup: string): Promise<{
  success: boolean
  message: string
  restoredData?: any
}> => {
  try {
    const session = getCurrentSession()
    if (!session) {
      return { success: false, message: 'يجب تسجيل الدخول أولاً' }
    }

    // فك تشفير البيانات
    const backupData: BackupData = decryptData(encryptedBackup)

    // التحقق من صحة البيانات
    if (!backupData.version || !backupData.timestamp || !backupData.userData) {
      return { success: false, message: 'النسخة الاحتياطية تالفة أو غير صحيحة' }
    }

    // التحقق من checksum
    const dataString = JSON.stringify(backupData.userData)
    const calculatedChecksum = await generateChecksum(dataString)
    
    if (calculatedChecksum !== backupData.checksum) {
      return { success: false, message: 'النسخة الاحتياطية تالفة - فشل في التحقق من سلامة البيانات' }
    }

    // التحقق من ملكية البيانات
    if (backupData.userId !== session.user.id) {
      logSecurityEvent({
        type: 'suspicious_activity',
        userId: session.user.id,
        details: {
          action: 'unauthorized_restore_attempt',
          backupUserId: backupData.userId
        }
      })
      return { success: false, message: 'غير مسموح باستعادة هذه النسخة الاحتياطية' }
    }

    // استعادة البيانات
    const { userData } = backupData
    
    // حفظ البيانات المستعادة
    if (userData.profile) saveToStorage('profile', userData.profile)
    if (userData.transactions) saveToStorage('transactions', userData.transactions)
    if (userData.invoices) saveToStorage('invoices', userData.invoices)
    if (userData.requests) saveToStorage('requests', userData.requests)
    if (userData.documents) saveToStorage('documents', userData.documents)
    if (userData.settings) saveToStorage('settings', userData.settings)

    // تسجيل العملية
    logSecurityEvent({
      type: 'data_access' as any,
      userId: session.user.id,
      details: {
        action: 'backup_restored',
        backupTimestamp: backupData.timestamp,
        restoredDataSize: encryptedBackup.length
      }
    })

    return {
      success: true,
      message: 'تم استعادة النسخة الاحتياطية بنجاح',
      restoredData: userData
    }

  } catch (error) {
    console.error('خطأ في استعادة النسخة الاحتياطية:', error)
    return { success: false, message: 'فشل في استعادة النسخة الاحتياطية' }
  }
}

// تحميل النسخة الاحتياطية كملف
export const downloadBackup = (encryptedBackup: string, filename?: string) => {
  try {
    const blob = new Blob([encryptedBackup], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    
    const link = document.createElement('a')
    link.href = url
    link.download = filename || `financial-backup-${new Date().toISOString().split('T')[0]}.json`
    
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    URL.revokeObjectURL(url)
    
    return true
  } catch (error) {
    console.error('خطأ في تحميل النسخة الاحتياطية:', error)
    return false
  }
}

// قراءة ملف النسخة الاحتياطية
export const readBackupFile = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string
        resolve(content)
      } catch (error) {
        reject(new Error('فشل في قراءة الملف'))
      }
    }
    
    reader.onerror = () => {
      reject(new Error('خطأ في قراءة الملف'))
    }
    
    reader.readAsText(file)
  })
}

// الحصول على قائمة النسخ الاحتياطية المحفوظة
export const getStoredBackups = (): BackupMetadata[] => {
  try {
    const backups = localStorage.getItem('financial_tracker_backups')
    return backups ? JSON.parse(backups) : []
  } catch (error) {
    console.error('خطأ في قراءة قائمة النسخ الاحتياطية:', error)
    return []
  }
}

// حذف نسخة احتياطية
export const deleteBackup = (backupId: string): boolean => {
  try {
    const backups = getStoredBackups()
    const filteredBackups = backups.filter(backup => backup.id !== backupId)
    localStorage.setItem('financial_tracker_backups', JSON.stringify(filteredBackups))
    
    const session = getCurrentSession()
    if (session) {
      logSecurityEvent({
        type: 'data_access' as any,
        userId: session.user.id,
        details: {
          action: 'backup_deleted',
          backupId
        }
      })
    }
    
    return true
  } catch (error) {
    console.error('خطأ في حذف النسخة الاحتياطية:', error)
    return false
  }
}

// دوال مساعدة
const generateBackupId = (): string => {
  return 'backup_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

const generateChecksum = async (data: string): Promise<string> => {
  // استخدام crypto API للحصول على hash
  if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    const hashBuffer = await window.crypto.subtle.digest('SHA-256', dataBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  } else {
    // fallback للمتصفحات القديمة
    let hash = 0
    for (let i = 0; i < data.length; i++) {
      const char = data.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // تحويل إلى 32bit integer
    }
    return hash.toString(16)
  }
}

// استيراد الدوال المطلوبة من storage.ts
const getStoredData = (key: string): any => {
  if (typeof window === 'undefined') return null
  try {
    const item = localStorage.getItem(`financialTracker_${key}`)
    return item ? JSON.parse(item) : null
  } catch (error) {
    console.error(`خطأ في قراءة ${key}:`, error)
    return null
  }
}

const saveToStorage = (key: string, data: any): boolean => {
  if (typeof window === 'undefined') return false
  try {
    localStorage.setItem(`financialTracker_${key}`, JSON.stringify(data))
    return true
  } catch (error) {
    console.error(`خطأ في حفظ ${key}:`, error)
    return false
  }
}
