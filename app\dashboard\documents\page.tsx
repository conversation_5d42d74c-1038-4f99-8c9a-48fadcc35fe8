'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import {
  FolderIcon,
  DocumentTextIcon,
  CloudArrowUpIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  EyeIcon,
  ArrowDownTrayIcon,
  TrashIcon,
  TagIcon,
  CalendarIcon,
  DocumentIcon,
  PhotoIcon,
  DocumentArrowUpIcon
} from '@heroicons/react/24/outline'

const documents: any[] = []

  const categories = [
    { id: 'all', name: 'جميع الفئات', count: documents.length },
    { id: 'كشوف حساب', name: 'كشوف حساب', count: documents.filter(d => d.category === 'كشوف حساب').length },
    { id: 'فواتير', name: 'فواتير', count: documents.filter(d => d.category === 'فواتير').length },
    { id: 'رواتب', name: 'رواتب', count: documents.filter(d => d.category === 'رواتب').length },
    { id: 'تأمين', name: 'تأمين', count: documents.filter(d => d.category === 'تأمين').length }
  ]

const statusConfig = {
  pending: { label: 'في الانتظار', color: 'text-yellow-600 bg-yellow-50' },
  processing: { label: 'جاري المعالجة', color: 'text-blue-600 bg-blue-50' },
  processed: { label: 'تم المعالجة', color: 'text-green-600 bg-green-50' },
  error: { label: 'خطأ', color: 'text-red-600 bg-red-50' }
}

const getFileIcon = (type: string) => {
  switch (type) {
    case 'pdf':
      return DocumentTextIcon
    case 'image':
      return PhotoIcon
    case 'document':
      return DocumentIcon
    default:
      return DocumentIcon
  }
}

export default function DocumentsPage() {
  const { userData, addDocument, deleteDocument } = useData()
  const { success } = useToastContext()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')
  const [dragActive, setDragActive] = useState(false)
  
  // استخدام المستندات من البيانات المحفوظة
  const documents = userData?.documents || []

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesCategory = selectedCategory === 'all' || doc.category === selectedCategory
    return matchesSearch && matchesCategory
  })

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true)
    } else if (e.type === 'dragleave') {
      setDragActive(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileUpload(e.dataTransfer.files[0])
    }
  }

  const handleFileUpload = (file: File) => {
    if (file.size > 10 * 1024 * 1024) {
      alert('حجم الملف أكبر من 10MB')
      return
    }

    const fileType = file.type.startsWith('image/') ? 'image' : 
                    file.type === 'application/pdf' ? 'pdf' : 'document'
    
    const category = file.name.includes('فاتورة') ? 'فواتير' :
                    file.name.includes('راتب') ? 'رواتب' :
                    file.name.includes('كشف') ? 'كشوف حساب' : 'عام'

    const newDocument = {
      title: file.name,
      type: fileType,
      category: category,
      uploadDate: new Date().toISOString().split('T')[0],
      size: Math.round(file.size / 1024),
      url: URL.createObjectURL(file),
      extractedData: null,
      tags: [category, fileType]
    }

    addDocument(newDocument)
    success(`تم رفع الملف: ${file.name} ✅`)
  }

  const handleViewDocument = (doc: any) => {
    if (doc.url) {
      window.open(doc.url, '_blank')
    } else {
      alert(`عرض مستند: ${doc.title}`)
    }
  }

  const handleDownloadDocument = (doc: any) => {
    if (doc.url) {
      const link = document.createElement('a')
      link.href = doc.url
      link.download = doc.title
      link.click()
    } else {
      alert(`تحميل مستند: ${doc.title}`)
    }
  }

  const handleDeleteDocument = (id: string, title: string) => {
    if (confirm(`هل أنت متأكد من حذف مستند "${title}"؟`)) {
      deleteDocument(id)
      success('تم حذف المستند بنجاح')
    }
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة المستندات</h1>
          <p className="mt-1 text-sm text-gray-600">
            رفع وأرشفة مستنداتك المالية بأمان
          </p>
        </div>
      </div>

      {/* Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors cursor-pointer ${
          dragActive
            ? 'border-primary-400 bg-primary-50'
            : 'border-gray-300 hover:border-primary-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={() => {
          const input = document.createElement('input')
          input.type = 'file'
          input.accept = '.pdf,.jpg,.jpeg,.png,.doc,.docx'
          input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files?.[0]
            if (file) {
              handleFileUpload(file)
            }
          }
          input.click()
        }}
      >
        <CloudArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900">رفع مستندات جديدة</h3>
        <p className="mt-1 text-sm text-gray-500">
          اسحب الملفات هنا أو 
          <button className="text-primary-600 hover:text-primary-700 font-medium mx-1">
            انقر للاختيار
          </button>
        </p>
        <p className="text-xs text-gray-400 mt-2">
          PDF، JPG، PNG، DOC، DOCX حتى 10MB لكل ملف
        </p>
        
        {/* AI Processing Info */}
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-700">
            🤖 سيتم معالجة المستندات تلقائياً باستخدام الذكاء الاصطناعي لاستخراج البيانات المهمة
          </p>
        </div>
      </div>

      {/* Stats and Filters */}
      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Categories Sidebar */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">الفئات</h2>
            <nav className="space-y-2">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`w-full flex items-center justify-between px-3 py-2 text-sm rounded-lg transition-colors ${
                    selectedCategory === category.id
                      ? 'bg-primary-100 text-primary-700'
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                >
                  <span>{category.name}</span>
                  <span className="text-xs bg-gray-200 text-gray-600 px-2 py-1 rounded-full">
                    {category.count}
                  </span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Documents Grid */}
        <div className="lg:col-span-3 space-y-6">
          {/* Search Bar */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4">
            <div className="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4 sm:space-x-reverse">
              <div className="relative flex-1">
                <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <input
                  type="text"
                  placeholder="البحث في المستندات..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field pr-10"
                />
              </div>
              <button className="btn-secondary flex items-center space-x-2 space-x-reverse">
                <FunnelIcon className="w-4 h-4" />
                <span>تصفية</span>
              </button>
            </div>
          </div>

          {/* Documents List */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            {filteredDocuments.length > 0 ? (
              <div className="divide-y divide-gray-200">
                {filteredDocuments.map((doc) => {
                  const FileIcon = getFileIcon(doc.type)
                  return (
                    <div key={doc.id} className="p-6 hover:bg-gray-50 transition-colors">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4 space-x-reverse">
                          <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                            <FileIcon className="w-6 h-6 text-gray-600" />
                          </div>
                          <div className="flex-1">
                            <h3 className="text-sm font-medium text-gray-900">{doc.title}</h3>
                            <div className="flex items-center space-x-4 space-x-reverse text-sm text-gray-500 mt-1">
                              <span className="flex items-center space-x-1 space-x-reverse">
                                <FolderIcon className="w-4 h-4" />
                                <span>{doc.category}</span>
                              </span>
                              <span className="flex items-center space-x-1 space-x-reverse">
                                <CalendarIcon className="w-4 h-4" />
                                <span>{doc.uploadDate}</span>
                              </span>
                              <span>{doc.size}</span>
                            </div>
                            <div className="flex items-center space-x-2 space-x-reverse mt-2">
                              <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                تم الرفع
                              </span>
                              <div className="flex items-center space-x-1 space-x-reverse">
                                <TagIcon className="w-3 h-3 text-gray-400" />
                                <div className="flex flex-wrap gap-1">
                                  {doc.tags.slice(0, 2).map((tag, index) => (
                                    <span key={index} className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                                      {tag}
                                    </span>
                                  ))}
                                  {doc.tags.length > 2 && (
                                    <span className="text-xs text-gray-400">
                                      +{doc.tags.length - 2}
                                    </span>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button 
                            onClick={() => handleViewDocument(doc)}
                            className="text-primary-600 hover:text-primary-700 p-2 rounded-lg hover:bg-primary-50"
                            title="عرض المستند"
                          >
                            <EyeIcon className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDownloadDocument(doc)}
                            className="text-gray-600 hover:text-gray-700 p-2 rounded-lg hover:bg-gray-50"
                            title="تحميل المستند"
                          >
                            <ArrowDownTrayIcon className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDeleteDocument(doc.id, doc.title)}
                            className="text-red-600 hover:text-red-700 p-2 rounded-lg hover:bg-red-50"
                            title="حذف المستند"
                          >
                            <TrashIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>
            ) : (
              <div className="text-center py-12">
                <FolderIcon className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">لا توجد مستندات</h3>
                <p className="mt-1 text-sm text-gray-500">
                  {searchTerm || selectedCategory !== 'all'
                    ? 'لم يتم العثور على نتائج مطابقة لبحثك'
                    : 'ابدأ برفع مستنداتك الأولى'
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* AI Features Info */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">🤖 ميزات الذكاء الاصطناعي</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <DocumentArrowUpIcon className="w-6 h-6 text-blue-600" />
            </div>
            <h3 className="font-medium text-gray-900">استخراج البيانات</h3>
            <p className="text-sm text-gray-600 mt-1">
              استخراج تلقائي للمعلومات المهمة من الفواتير والكشوف
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <TagIcon className="w-6 h-6 text-green-600" />
            </div>
            <h3 className="font-medium text-gray-900">التصنيف التلقائي</h3>
            <p className="text-sm text-gray-600 mt-1">
              تصنيف المستندات تلقائياً حسب النوع والفئة
            </p>
          </div>
          <div className="text-center">
            <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
              <MagnifyingGlassIcon className="w-6 h-6 text-purple-600" />
            </div>
            <h3 className="font-medium text-gray-900">البحث الذكي</h3>
            <p className="text-sm text-gray-600 mt-1">
              البحث في محتوى المستندات حتى لو كان نص مكتوب بخط اليد
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}