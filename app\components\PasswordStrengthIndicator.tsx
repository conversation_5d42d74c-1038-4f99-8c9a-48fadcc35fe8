'use client'

import { useState, useEffect } from 'react'
import { 
  CheckCircleIcon, 
  XCircleIcon, 
  ExclamationTriangleIcon 
} from '@heroicons/react/24/outline'

interface PasswordStrengthProps {
  password: string
  showRequirements?: boolean
  className?: string
}

interface PasswordRequirement {
  id: string
  label: string
  test: (password: string) => boolean
  met: boolean
}

export default function PasswordStrengthIndicator({ 
  password, 
  showRequirements = true,
  className = '' 
}: PasswordStrengthProps) {
  const [strength, setStrength] = useState(0)
  const [requirements, setRequirements] = useState<PasswordRequirement[]>([])

  const passwordRequirements: Omit<PasswordRequirement, 'met'>[] = [
    {
      id: 'length',
      label: 'على الأقل 8 أحرف',
      test: (pwd) => pwd.length >= 8
    },
    {
      id: 'uppercase',
      label: 'حرف كبير واحد على الأقل',
      test: (pwd) => /[A-Z]/.test(pwd)
    },
    {
      id: 'lowercase',
      label: 'حرف صغير واحد على الأقل',
      test: (pwd) => /[a-z]/.test(pwd)
    },
    {
      id: 'number',
      label: 'رقم واحد على الأقل',
      test: (pwd) => /\d/.test(pwd)
    },
    {
      id: 'special',
      label: 'رمز خاص واحد على الأقل (!@#$%^&*)',
      test: (pwd) => /[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(pwd)
    },
    {
      id: 'noCommon',
      label: 'ليست كلمة مرور شائعة',
      test: (pwd) => !isCommonPassword(pwd)
    }
  ]

  useEffect(() => {
    calculateStrength()
  }, [password])

  const calculateStrength = () => {
    if (!password) {
      setStrength(0)
      setRequirements(passwordRequirements.map(req => ({ ...req, met: false })))
      return
    }

    const updatedRequirements = passwordRequirements.map(req => ({
      ...req,
      met: req.test(password)
    }))

    setRequirements(updatedRequirements)

    // حساب القوة بناءً على المتطلبات المحققة
    const metRequirements = updatedRequirements.filter(req => req.met).length
    const strengthPercentage = (metRequirements / passwordRequirements.length) * 100

    setStrength(strengthPercentage)
  }

  const getStrengthLabel = (): string => {
    if (strength === 0) return ''
    if (strength < 30) return 'ضعيفة جداً'
    if (strength < 50) return 'ضعيفة'
    if (strength < 70) return 'متوسطة'
    if (strength < 90) return 'قوية'
    return 'قوية جداً'
  }

  const getStrengthColor = (): string => {
    if (strength === 0) return 'bg-gray-200'
    if (strength < 30) return 'bg-red-500'
    if (strength < 50) return 'bg-orange-500'
    if (strength < 70) return 'bg-yellow-500'
    if (strength < 90) return 'bg-blue-500'
    return 'bg-green-500'
  }

  const getStrengthTextColor = (): string => {
    if (strength === 0) return 'text-gray-500'
    if (strength < 30) return 'text-red-600'
    if (strength < 50) return 'text-orange-600'
    if (strength < 70) return 'text-yellow-600'
    if (strength < 90) return 'text-blue-600'
    return 'text-green-600'
  }

  const isCommonPassword = (pwd: string): boolean => {
    const commonPasswords = [
      '123456', 'password', '123456789', '12345678', '12345',
      '1234567', '1234567890', 'qwerty', 'abc123', 'password123',
      'admin', 'letmein', 'welcome', 'monkey', '1234',
      'dragon', 'master', 'hello', 'login', 'pass'
    ]
    return commonPasswords.includes(pwd.toLowerCase())
  }

  if (!password) return null

  return (
    <div className={`mt-2 ${className}`}>
      {/* شريط القوة */}
      <div className="mb-2">
        <div className="flex items-center justify-between mb-1">
          <span className="text-sm font-medium text-gray-700">قوة كلمة المرور</span>
          <span className={`text-sm font-medium ${getStrengthTextColor()}`}>
            {getStrengthLabel()}
          </span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2">
          <div
            className={`h-2 rounded-full transition-all duration-300 ${getStrengthColor()}`}
            style={{ width: `${strength}%` }}
          />
        </div>
      </div>

      {/* متطلبات كلمة المرور */}
      {showRequirements && (
        <div className="space-y-1">
          <p className="text-sm font-medium text-gray-700 mb-2">متطلبات كلمة المرور:</p>
          {requirements.map((requirement) => (
            <div key={requirement.id} className="flex items-center space-x-2">
              {requirement.met ? (
                <CheckCircleIcon className="w-4 h-4 text-green-500 flex-shrink-0" />
              ) : (
                <XCircleIcon className="w-4 h-4 text-gray-400 flex-shrink-0" />
              )}
              <span className={`text-sm ${
                requirement.met ? 'text-green-600' : 'text-gray-500'
              }`}>
                {requirement.label}
              </span>
            </div>
          ))}
        </div>
      )}

      {/* تحذيرات إضافية */}
      {password && strength < 50 && (
        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <div className="flex">
            <ExclamationTriangleIcon className="w-5 h-5 text-yellow-400 ml-2 flex-shrink-0" />
            <div className="text-sm text-yellow-800">
              <p className="font-medium">كلمة المرور ضعيفة</p>
              <p className="mt-1">
                استخدم كلمة مرور أقوى لحماية حسابك بشكل أفضل. 
                تأكد من تضمين أحرف كبيرة وصغيرة وأرقام ورموز خاصة.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* نصائح لكلمة مرور قوية */}
      {password && strength >= 90 && (
        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded-lg">
          <div className="flex">
            <CheckCircleIcon className="w-5 h-5 text-green-400 ml-2 flex-shrink-0" />
            <div className="text-sm text-green-800">
              <p className="font-medium">ممتاز! كلمة مرور قوية</p>
              <p className="mt-1">
                كلمة المرور الخاصة بك قوية وآمنة. تذكر عدم مشاركتها مع أي شخص.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

// مكون مدخل كلمة المرور مع مؤشر القوة
interface SecurePasswordInputProps {
  name: string
  placeholder?: string
  required?: boolean
  className?: string
  value?: string
  onChange?: (value: string) => void
  showStrengthIndicator?: boolean
  showRequirements?: boolean
}

export function SecurePasswordInput({
  name,
  placeholder,
  required = false,
  className = '',
  value,
  onChange,
  showStrengthIndicator = true,
  showRequirements = true
}: SecurePasswordInputProps) {
  const [password, setPassword] = useState(value || '')
  const [showPassword, setShowPassword] = useState(false)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setPassword(newValue)
    if (onChange) {
      onChange(newValue)
    }
  }

  return (
    <div>
      <div className="relative">
        <input
          type={showPassword ? 'text' : 'password'}
          name={name}
          placeholder={placeholder}
          required={required}
          className={`
            block w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm
            focus:outline-none focus:ring-primary-500 focus:border-primary-500
            ${className}
          `}
          value={password}
          onChange={handleChange}
          autoComplete="new-password"
        />
        <button
          type="button"
          onClick={() => setShowPassword(!showPassword)}
          className="absolute inset-y-0 left-0 pl-3 flex items-center"
        >
          <span className="text-gray-400 hover:text-gray-600 text-sm">
            {showPassword ? 'إخفاء' : 'إظهار'}
          </span>
        </button>
      </div>
      
      {showStrengthIndicator && (
        <PasswordStrengthIndicator 
          password={password} 
          showRequirements={showRequirements}
        />
      )}
    </div>
  )
}
