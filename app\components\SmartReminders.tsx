'use client'

import { useState, useEffect } from 'react'
import { useData } from '@/contexts/DataContext'
import { useNotifications } from '@/contexts/NotificationContext'
import {
  BellIcon,
  ExclamationTriangleIcon,
  ClockIcon,
  CheckCircleIcon,
  XMarkIcon
} from '@heroicons/react/24/outline'

interface Reminder {
  id: string
  title: string
  message: string
  type: 'bill_due' | 'payment_reminder' | 'budget_alert'
  priority: 'low' | 'medium' | 'high'
  dueDate: string
  amount?: number
  dismissed: boolean
}

export default function SmartReminders() {
  const { userData } = useData()
  const { addNotification } = useNotifications()
  const [reminders, setReminders] = useState<Reminder[]>([])

  useEffect(() => {
    generateSmartReminders()
  }, [userData])

  const generateSmartReminders = () => {
    const newReminders: Reminder[] = []
    const today = new Date()
    const invoices = userData?.invoices || []

    // تذكيرات الفواتير المستحقة
    invoices.forEach(invoice => {
      if (invoice.status === 'pending' || invoice.status === 'overdue') {
        const dueDate = new Date(invoice.dueDate)
        const daysUntilDue = Math.ceil((dueDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24))
        
        if (daysUntilDue <= 7 && daysUntilDue >= 0) {
          newReminders.push({
            id: `bill_${invoice.id}`,
            title: `فاتورة مستحقة قريباً`,
            message: `فاتورة ${invoice.title} مستحقة خلال ${daysUntilDue} أيام`,
            type: 'bill_due',
            priority: daysUntilDue <= 2 ? 'high' : 'medium',
            dueDate: invoice.dueDate,
            amount: invoice.amount,
            dismissed: false
          })
        } else if (daysUntilDue < 0) {
          newReminders.push({
            id: `overdue_${invoice.id}`,
            title: `فاتورة متأخرة`,
            message: `فاتورة ${invoice.title} متأخرة ${Math.abs(daysUntilDue)} يوم`,
            type: 'bill_due',
            priority: 'high',
            dueDate: invoice.dueDate,
            amount: invoice.amount,
            dismissed: false
          })
        }
      }
    })

    // تذكيرات الميزانية
    const transactions = userData?.transactions || []
    const thisMonthExpenses = transactions
      .filter(t => {
        const transactionDate = new Date(t.date)
        return t.amount < 0 && 
               transactionDate.getMonth() === today.getMonth() &&
               transactionDate.getFullYear() === today.getFullYear()
      })
      .reduce((sum, t) => sum + Math.abs(t.amount), 0)

    if (thisMonthExpenses > 5000) {
      newReminders.push({
        id: 'budget_alert',
        title: 'تنبيه الميزانية',
        message: `مصروفاتك هذا الشهر وصلت إلى ${thisMonthExpenses} ر.س`,
        type: 'budget_alert',
        priority: thisMonthExpenses > 8000 ? 'high' : 'medium',
        dueDate: today.toISOString().split('T')[0],
        amount: thisMonthExpenses,
        dismissed: false
      })
    }

    setReminders(newReminders)
  }

  const dismissReminder = (id: string) => {
    setReminders(prev => prev.map(r => 
      r.id === id ? { ...r, dismissed: true } : r
    ))
  }

  const handleReminderAction = (reminder: Reminder) => {
    addNotification({
      title: reminder.title,
      message: reminder.message,
      type: reminder.priority === 'high' ? 'warning' : 'info',
      priority: reminder.priority
    })
    dismissReminder(reminder.id)
  }

  const activeReminders = reminders.filter(r => !r.dismissed)

  if (activeReminders.length === 0) {
    return null
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'border-red-200 bg-red-50'
      case 'medium':
        return 'border-yellow-200 bg-yellow-50'
      default:
        return 'border-blue-200 bg-blue-50'
    }
  }

  const getPriorityIcon = (type: string, priority: string) => {
    if (priority === 'high') {
      return <ExclamationTriangleIcon className="w-5 h-5 text-red-600" />
    }
    switch (type) {
      case 'bill_due':
        return <ClockIcon className="w-5 h-5 text-yellow-600" />
      case 'budget_alert':
        return <BellIcon className="w-5 h-5 text-blue-600" />
      default:
        return <CheckCircleIcon className="w-5 h-5 text-green-600" />
    }
  }

  return (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-semibold text-gray-900">التذكيرات الذكية</h2>
        <span className="bg-red-100 text-red-800 text-xs font-medium px-2.5 py-0.5 rounded-full">
          {activeReminders.length}
        </span>
      </div>

      <div className="space-y-3">
        {activeReminders.map((reminder) => (
          <div
            key={reminder.id}
            className={`p-4 rounded-lg border ${getPriorityColor(reminder.priority)} transition-all duration-200`}
          >
            <div className="flex items-start justify-between">
              <div className="flex items-start space-x-3 space-x-reverse">
                <div className="flex-shrink-0 mt-0.5">
                  {getPriorityIcon(reminder.type, reminder.priority)}
                </div>
                <div className="flex-1">
                  <h3 className="text-sm font-medium text-gray-900">
                    {reminder.title}
                  </h3>
                  <p className="text-sm text-gray-600 mt-1">
                    {reminder.message}
                  </p>
                  {reminder.amount && (
                    <p className="text-sm font-semibold text-gray-900 mt-1">
                      المبلغ: {reminder.amount} ر.س
                    </p>
                  )}
                  <div className="flex items-center space-x-3 space-x-reverse mt-3">
                    <button
                      onClick={() => handleReminderAction(reminder)}
                      className="text-xs bg-primary-600 text-white px-3 py-1 rounded hover:bg-primary-700 transition-colors"
                    >
                      إضافة للإشعارات
                    </button>
                    <button
                      onClick={() => dismissReminder(reminder.id)}
                      className="text-xs text-gray-500 hover:text-gray-700"
                    >
                      تجاهل
                    </button>
                  </div>
                </div>
              </div>
              <button
                onClick={() => dismissReminder(reminder.id)}
                className="text-gray-400 hover:text-gray-600 ml-2"
              >
                <XMarkIcon className="w-4 h-4" />
              </button>
            </div>
          </div>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-gray-200">
        <p className="text-xs text-gray-500 text-center">
          💡 التذكيرات الذكية تساعدك على عدم نسيان المواعيد المهمة
        </p>
      </div>
    </div>
  )
}