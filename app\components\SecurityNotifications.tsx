'use client'

import { useState, useEffect } from 'react'
import { 
  BellIcon,
  ExclamationTriangleIcon,
  ShieldExclamationIcon,
  CheckCircleIcon,
  XMarkIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline'
import { getCurrentSession } from '@/lib/auth'

interface SecurityNotification {
  id: string
  type: 'warning' | 'error' | 'info' | 'success'
  title: string
  message: string
  timestamp: string
  read: boolean
  actionRequired?: boolean
  action?: {
    label: string
    onClick: () => void
  }
}

interface SecurityNotificationsProps {
  className?: string
  maxNotifications?: number
  showUnreadOnly?: boolean
}

export default function SecurityNotifications({
  className = '',
  maxNotifications = 5,
  showUnreadOnly = false
}: SecurityNotificationsProps) {
  const [notifications, setNotifications] = useState<SecurityNotification[]>([])
  const [showDropdown, setShowDropdown] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSecurityNotifications()
    
    // تحديث الإشعارات كل دقيقة
    const interval = setInterval(loadSecurityNotifications, 60000)
    return () => clearInterval(interval)
  }, [])

  const loadSecurityNotifications = () => {
    try {
      const session = getCurrentSession()
      if (!session) return

      const logs = JSON.parse(localStorage.getItem('security_logs') || '[]')
      const existingNotifications = JSON.parse(localStorage.getItem('security_notifications') || '[]')
      
      // إنشاء إشعارات جديدة بناءً على السجلات الأمنية
      const newNotifications = generateNotificationsFromLogs(logs)
      
      // دمج الإشعارات الموجودة مع الجديدة
      const allNotifications = [...existingNotifications, ...newNotifications]
        .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
        .slice(0, 50) // الاحتفاظ بآخر 50 إشعار

      // حفظ الإشعارات المحدثة
      localStorage.setItem('security_notifications', JSON.stringify(allNotifications))
      
      // تطبيق الفلاتر
      let filteredNotifications = allNotifications
      if (showUnreadOnly) {
        filteredNotifications = filteredNotifications.filter(n => !n.read)
      }
      
      setNotifications(filteredNotifications.slice(0, maxNotifications))
      setLoading(false)
    } catch (error) {
      console.error('خطأ في تحميل إشعارات الأمان:', error)
      setLoading(false)
    }
  }

  const generateNotificationsFromLogs = (logs: any[]): SecurityNotification[] => {
    const notifications: SecurityNotification[] = []
    const recentLogs = logs.filter(log => {
      const logTime = new Date(log.timestamp).getTime()
      const oneHourAgo = Date.now() - (60 * 60 * 1000)
      return logTime > oneHourAgo
    })

    // إشعارات محاولات الدخول الفاشلة
    const failedLogins = recentLogs.filter(log => log.type === 'failed_login')
    if (failedLogins.length > 3) {
      notifications.push({
        id: `failed_login_${Date.now()}`,
        type: 'error',
        title: 'محاولات دخول مشبوهة',
        message: `تم رصد ${failedLogins.length} محاولة دخول فاشلة في الساعة الماضية`,
        timestamp: new Date().toISOString(),
        read: false,
        actionRequired: true,
        action: {
          label: 'مراجعة السجل',
          onClick: () => console.log('مراجعة سجل الأمان')
        }
      })
    }

    // إشعارات الأنشطة المشبوهة
    const suspiciousActivities = recentLogs.filter(log => log.type === 'suspicious_activity')
    suspiciousActivities.forEach(activity => {
      notifications.push({
        id: `suspicious_${activity.id || Date.now()}`,
        type: 'warning',
        title: 'نشاط مشبوه',
        message: 'تم رصد نشاط مشبوه في حسابك',
        timestamp: activity.timestamp,
        read: false,
        actionRequired: true,
        action: {
          label: 'مراجعة التفاصيل',
          onClick: () => console.log('مراجعة النشاط المشبوه')
        }
      })
    })

    // إشعارات تغيير كلمة المرور
    const passwordChanges = recentLogs.filter(log => log.type === 'password_change')
    passwordChanges.forEach(change => {
      notifications.push({
        id: `password_change_${change.id || Date.now()}`,
        type: 'success',
        title: 'تم تغيير كلمة المرور',
        message: 'تم تغيير كلمة المرور بنجاح',
        timestamp: change.timestamp,
        read: false
      })
    })

    return notifications
  }

  const markAsRead = (notificationId: string) => {
    const updatedNotifications = notifications.map(n => 
      n.id === notificationId ? { ...n, read: true } : n
    )
    setNotifications(updatedNotifications)
    
    // تحديث التخزين المحلي
    const allNotifications = JSON.parse(localStorage.getItem('security_notifications') || '[]')
    const updatedAll = allNotifications.map((n: SecurityNotification) => 
      n.id === notificationId ? { ...n, read: true } : n
    )
    localStorage.setItem('security_notifications', JSON.stringify(updatedAll))
  }

  const markAllAsRead = () => {
    const updatedNotifications = notifications.map(n => ({ ...n, read: true }))
    setNotifications(updatedNotifications)
    
    const allNotifications = JSON.parse(localStorage.getItem('security_notifications') || '[]')
    const updatedAll = allNotifications.map((n: SecurityNotification) => ({ ...n, read: true }))
    localStorage.setItem('security_notifications', JSON.stringify(updatedAll))
  }

  const deleteNotification = (notificationId: string) => {
    const updatedNotifications = notifications.filter(n => n.id !== notificationId)
    setNotifications(updatedNotifications)
    
    const allNotifications = JSON.parse(localStorage.getItem('security_notifications') || '[]')
    const updatedAll = allNotifications.filter((n: SecurityNotification) => n.id !== notificationId)
    localStorage.setItem('security_notifications', JSON.stringify(updatedAll))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'error':
        return <ShieldExclamationIcon className="w-5 h-5 text-red-500" />
      case 'warning':
        return <ExclamationTriangleIcon className="w-5 h-5 text-yellow-500" />
      case 'success':
        return <CheckCircleIcon className="w-5 h-5 text-green-500" />
      case 'info':
      default:
        return <InformationCircleIcon className="w-5 h-5 text-blue-500" />
    }
  }

  const getNotificationBgColor = (type: string) => {
    switch (type) {
      case 'error':
        return 'bg-red-50 border-red-200'
      case 'warning':
        return 'bg-yellow-50 border-yellow-200'
      case 'success':
        return 'bg-green-50 border-green-200'
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200'
    }
  }

  const unreadCount = notifications.filter(n => !n.read).length

  if (loading) {
    return (
      <div className={`relative ${className}`}>
        <div className="animate-pulse">
          <div className="w-6 h-6 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className={`relative ${className}`}>
      {/* زر الإشعارات */}
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        className="relative p-2 text-gray-600 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary-500 rounded-lg"
      >
        <BellIcon className="w-6 h-6" />
        {unreadCount > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {unreadCount > 9 ? '9+' : unreadCount}
          </span>
        )}
      </button>

      {/* قائمة الإشعارات المنسدلة */}
      {showDropdown && (
        <div className="absolute left-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold text-gray-900">إشعارات الأمان</h3>
              {unreadCount > 0 && (
                <button
                  onClick={markAllAsRead}
                  className="text-sm text-primary-600 hover:text-primary-800"
                >
                  تحديد الكل كمقروء
                </button>
              )}
            </div>
          </div>

          <div className="max-h-96 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                <BellIcon className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                <p>لا توجد إشعارات أمنية</p>
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={`p-4 border-b border-gray-100 last:border-b-0 ${
                    !notification.read ? 'bg-gray-50' : ''
                  }`}
                >
                  <div className="flex items-start space-x-3 space-x-reverse">
                    <div className="flex-shrink-0 mt-1">
                      {getNotificationIcon(notification.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900">
                          {notification.title}
                        </p>
                        <button
                          onClick={() => deleteNotification(notification.id)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <XMarkIcon className="w-4 h-4" />
                        </button>
                      </div>
                      <p className="text-sm text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-500 mt-2">
                        {new Date(notification.timestamp).toLocaleString('ar-SA')}
                      </p>
                      
                      <div className="flex items-center space-x-2 space-x-reverse mt-2">
                        {!notification.read && (
                          <button
                            onClick={() => markAsRead(notification.id)}
                            className="text-xs text-primary-600 hover:text-primary-800"
                          >
                            تحديد كمقروء
                          </button>
                        )}
                        {notification.action && (
                          <button
                            onClick={notification.action.onClick}
                            className="text-xs text-primary-600 hover:text-primary-800 font-medium"
                          >
                            {notification.action.label}
                          </button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}

      {/* خلفية لإغلاق القائمة المنسدلة */}
      {showDropdown && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowDropdown(false)}
        />
      )}
    </div>
  )
}
