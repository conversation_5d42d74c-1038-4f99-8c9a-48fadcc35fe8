'use client'

import { useState, useEffect } from 'react'
import { 
  ShieldCheckIcon, 
  KeyIcon, 
  EyeIcon,
  ClockIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import ProtectedRoute from '@/components/ProtectedRoute'
import BackupManager from '@/components/BackupManager'
import SecurityDashboard from '@/components/SecurityDashboard'
import { SecurityTabsWrapper } from '@/components/SecurityTabs'
import { getCurrentSession, changePassword } from '@/lib/auth'
import { useToastContext } from '@/contexts/ToastContext'
import SecureForm, { SecureInput } from '@/components/SecureForm'
import { SecurePasswordInput } from '@/components/PasswordStrengthIndicator'

export default function SecurityPage() {
  const [showPasswordForm, setShowPasswordForm] = useState(false)
  const [securityLogs, setSecurityLogs] = useState<any[]>([])
  const { success, error } = useToastContext()

  useEffect(() => {
    loadSecurityLogs()
  }, [])

  const loadSecurityLogs = () => {
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]')
    setSecurityLogs(logs.slice(-10).reverse()) // آخر 10 سجلات
  }

  const handlePasswordChange = async (data: any) => {
    try {
      const result = await changePassword(data.currentPassword, data.newPassword)
      if (result.success) {
        success('تم تغيير كلمة المرور بنجاح')
        setShowPasswordForm(false)
        loadSecurityLogs()
      } else {
        error(result.message)
      }
    } catch (err) {
      error('فشل في تغيير كلمة المرور')
    }
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* العنوان الرئيسي */}
          <div className="mb-8">
            <div className="flex items-center">
              <ShieldCheckIcon className="w-8 h-8 text-primary-600 ml-3" />
              <h1 className="text-3xl font-bold text-gray-900">إعدادات الأمان</h1>
            </div>
            <p className="mt-2 text-gray-600">
              إدارة إعدادات الأمان وحماية حسابك وبياناتك المالية
            </p>
          </div>

          {/* نظام التبويبات للأمان */}
          <SecurityTabsWrapper
            overviewContent={<SecurityDashboard />}
            passwordContent={
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center justify-between mb-6">
                  <div className="flex items-center">
                    <KeyIcon className="w-6 h-6 text-primary-600 ml-3" />
                    <h2 className="text-xl font-semibold text-gray-900">إدارة كلمة المرور</h2>
                  </div>
                  <button
                    onClick={() => setShowPasswordForm(!showPasswordForm)}
                    className="text-sm text-primary-600 hover:text-primary-800"
                  >
                    {showPasswordForm ? 'إلغاء' : 'تغيير كلمة المرور'}
                  </button>
                </div>

                {showPasswordForm ? (
                  <SecureForm onSubmit={handlePasswordChange}>
                    <div className="space-y-4">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كلمة المرور الحالية
                        </label>
                        <SecureInput
                          type="password"
                          name="currentPassword"
                          required
                          className="w-full"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          كلمة المرور الجديدة
                        </label>
                        <SecurePasswordInput
                          name="newPassword"
                          placeholder="أدخل كلمة المرور الجديدة"
                          required
                          className="w-full"
                          showStrengthIndicator={true}
                          showRequirements={true}
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">
                          تأكيد كلمة المرور الجديدة
                        </label>
                        <SecureInput
                          type="password"
                          name="confirmPassword"
                          required
                          minLength={8}
                          className="w-full"
                          placeholder="أعد إدخال كلمة المرور الجديدة"
                        />
                      </div>

                      <button
                        type="submit"
                        className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                      >
                        تغيير كلمة المرور
                      </button>
                    </div>
                  </SecureForm>
                ) : (
                  <div className="text-gray-600">
                    <p>كلمة المرور محمية ومشفرة بقوة</p>
                    <p className="text-sm mt-2">آخر تغيير: منذ أسبوع</p>
                  </div>
                )}
              </div>
            }
            activityContent={
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center mb-6">
                  <EyeIcon className="w-6 h-6 text-primary-600 ml-3" />
                  <h2 className="text-xl font-semibold text-gray-900">سجل الأنشطة الأمنية</h2>
                </div>

                <div className="space-y-3 max-h-96 overflow-y-auto">
                  {securityLogs.length === 0 ? (
                    <p className="text-gray-500 text-center py-8">لا توجد أنشطة مسجلة</p>
                  ) : (
                    securityLogs.map((log, index) => (
                      <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0">
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {log.type === 'login' && 'تسجيل دخول'}
                            {log.type === 'logout' && 'تسجيل خروج'}
                            {log.type === 'failed_login' && 'فشل تسجيل دخول'}
                            {log.type === 'password_change' && 'تغيير كلمة المرور'}
                            {log.type === 'suspicious_activity' && 'نشاط مشبوه'}
                          </p>
                          <p className="text-xs text-gray-500">
                            {new Date(log.timestamp).toLocaleString('ar-SA')}
                          </p>
                        </div>
                        <div className={`w-3 h-3 rounded-full ${
                          log.type === 'login' ? 'bg-green-500' :
                          log.type === 'logout' ? 'bg-gray-500' :
                          log.type === 'failed_login' ? 'bg-red-500' :
                          log.type === 'suspicious_activity' ? 'bg-orange-500' :
                          'bg-blue-500'
                        }`}></div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            }
            backupContent={<BackupManager />}
            defaultTab="overview"
          />
        </div>
      </div>
    </ProtectedRoute>
  )
}
