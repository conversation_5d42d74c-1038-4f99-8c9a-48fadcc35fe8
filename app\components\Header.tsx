'use client'

import Link from 'next/link'
import { useState } from 'react'
import { 
  Bars3Icon, 
  XMarkIcon,
  UserIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'
import GlobalSearch from './GlobalSearch'
import NotificationDropdown from './NotificationDropdown'

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const navigation = [
    { name: 'الرئيسية', href: '/' },
    { name: 'لوحة التحكم', href: '/dashboard' },
    { name: 'الميزانية', href: '/dashboard/budget' },
    { name: 'الأهداف', href: '/dashboard/goals' },
    { name: 'الاستثمارات', href: '/dashboard/investments' },
    { name: 'الضرائب', href: '/dashboard/taxes' },
    { name: 'التأمين', href: '/dashboard/insurance' },
    { name: 'القروض', href: '/dashboard/loans' },
    { name: 'الفواتير', href: '/dashboard/invoices' }
  ]

  return (
    <header className="bg-white shadow-sm border-b border-gray-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center">
            <Link href="/" className="flex items-center space-x-2 space-x-reverse">
              <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">م</span>
              </div>
              <span className="text-xl font-bold text-gray-900">متابعة مالية</span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8 space-x-reverse">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                className="text-gray-600 hover:text-primary-600 px-3 py-2 text-sm font-medium transition-colors"
              >
                {item.name}
              </Link>
            ))}
          </nav>

          {/* Search Bar */}
          <div className="hidden lg:block flex-1 max-w-lg mx-8">
            <GlobalSearch />
          </div>

          {/* User Actions */}
          <div className="flex items-center space-x-4 space-x-reverse">
            <NotificationDropdown />
            <Link 
              href="/dashboard/settings"
              className="p-2 text-gray-600 hover:text-primary-600 transition-colors"
              title="الإعدادات"
            >
              <Cog6ToothIcon className="w-5 h-5" />
            </Link>
            <div className="flex space-x-2 space-x-reverse">
              <Link 
                href="/auth/login"
                className="flex items-center space-x-2 space-x-reverse bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors"
              >
                <UserIcon className="w-4 h-4" />
                <span className="text-sm font-medium">تسجيل الدخول</span>
              </Link>
              <Link 
                href="/auth/register"
                className="flex items-center space-x-2 space-x-reverse border border-primary-600 text-primary-600 px-4 py-2 rounded-lg hover:bg-primary-50 transition-colors"
              >
                <span className="text-sm font-medium">إنشاء حساب</span>
              </Link>
            </div>

            {/* Mobile menu button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 text-gray-600 hover:text-primary-600"
            >
              {isMenuOpen ? (
                <XMarkIcon className="w-6 h-6" />
              ) : (
                <Bars3Icon className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-200 py-4">
            <nav className="space-y-2">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="block px-3 py-2 text-gray-600 hover:text-primary-600 hover:bg-gray-50 rounded-md transition-colors"
                  onClick={() => setIsMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}
            </nav>
          </div>
        )}
      </div>
    </header>
  )
}