import Link from 'next/link'
import Header from '../components/Header'
import Footer from '../components/Footer'
import {
  DocumentTextIcon,
  ChartBarIcon,
  FolderIcon,
  PlusCircleIcon,
  ClockIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  CheckCircleIcon,
  StarIcon
} from '@heroicons/react/24/outline'

const services = [
  {
    id: 1,
    title: 'إدارة الفواتير',
    description: 'إدارة وتتبع جميع فواتيرك المالية بسهولة مع تذكيرات الاستحقاق',
    icon: DocumentTextIcon,
    features: ['رفع الفواتير', 'تتبع الاستحقاق', 'تذكيرات تلقائية', 'تصنيف ذكي'],
    color: 'bg-blue-50 text-blue-600',
    href: '/dashboard/invoices'
  },
  {
    id: 2,
    title: 'تقارير الإنفاق',
    description: 'احصل على تقارير مفصلة عن نمط إنفاقك مع تحليل ذكي وتوصيات',
    icon: ChartBarIcon,
    features: ['تحليل الإنفاق', 'رسوم بيانية', 'توقعات مستقبلية', 'مقارنات شهرية'],
    color: 'bg-green-50 text-green-600',
    href: '/dashboard/reports'
  },
  {
    id: 3,
    title: 'رفع المستندات',
    description: 'رفع وأرشفة مستنداتك المالية بأمان مع معالجة ذكية بالذكاء الاصطناعي',
    icon: FolderIcon,
    features: ['رفع آمن', 'معالجة ذكية', 'استخراج البيانات', 'أرشفة منظمة'],
    color: 'bg-purple-50 text-purple-600',
    href: '/dashboard/documents'
  },
  {
    id: 4,
    title: 'تقديم طلب مالي',
    description: 'قدم طلبات السلف والخدمات المالية مع متابعة حالة الطلب',
    icon: PlusCircleIcon,
    features: ['طلبات سلف', 'خدمات مالية', 'متابعة الحالة', 'إشعارات فورية'],
    color: 'bg-orange-50 text-orange-600',
    href: '/dashboard/requests'
  },
  {
    id: 5,
    title: 'مراجعة العمليات السابقة',
    description: 'تتبع ومراجعة جميع عملياتك المالية مع إمكانية التصدير والتحليل',
    icon: ClockIcon,
    features: ['سجل شامل', 'بحث متقدم', 'تصدير البيانات', 'تحليل الاتجاهات'],
    color: 'bg-red-50 text-red-600',
    href: '/dashboard/history'
  },
  {
    id: 6,
    title: 'إعدادات الحساب',
    description: 'إدارة بياناتك الشخصية وإعدادات الأمان والخصوصية',
    icon: ShieldCheckIcon,
    features: ['إدارة الملف الشخصي', 'إعدادات الأمان', 'إدارة الإشعارات', 'الخصوصية'],
    color: 'bg-gray-50 text-gray-600',
    href: '/dashboard/settings'
  }
]

const features = [
  {
    title: 'أمان عالي المستوى',
    description: 'نستخدم أحدث تقنيات التشفير لحماية بياناتك المالية',
    icon: ShieldCheckIcon
  },
  {
    title: 'ذكاء اصطناعي متقدم',
    description: 'معالجة ذكية للمستندات واستخراج البيانات تلقائياً',
    icon: ChartBarIcon
  },
  {
    title: 'واجهة سهلة الاستخدام',
    description: 'تصميم بديهي يجعل إدارة أموالك أمراً سهلاً',
    icon: UserGroupIcon
  },
  {
    title: 'دعم فني متميز',
    description: 'فريق دعم متاح 24/7 لمساعدتك في أي وقت',
    icon: CheckCircleIcon
  }
]

const testimonials = [
  {
    name: 'أحمد محمد',
    role: 'مدير مالي',
    content: 'منصة رائعة ساعدتني في تنظيم أموري المالية بشكل احترافي. التقارير مفصلة والواجهة سهلة الاستخدام.',
    rating: 5
  },
  {
    name: 'فاطمة العلي',
    role: 'ربة منزل',
    content: 'أصبح بإمكاني متابعة مصروفات البيت بسهولة. الإشعارات التلقائية تذكرني بمواعيد دفع الفواتير.',
    rating: 5
  },
  {
    name: 'خالد السعد',
    role: 'صاحب عمل',
    content: 'الذكاء الاصطناعي يوفر علي الكثير من الوقت في معالجة الفواتير. خدمة ممتازة وأنصح بها.',
    rating: 5
  }
]

export default function ServicesPage() {
  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
            خدماتنا المالية
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            مجموعة شاملة من الخدمات المالية الذكية المصممة لتلبية جميع احتياجاتك المالية
          </p>
          <Link href="/auth/login" className="btn-primary px-8 py-3 text-lg">
            ابدأ الآن مجاناً
          </Link>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">خدماتنا المتكاملة</h2>
            <p className="text-lg text-gray-600">
              كل ما تحتاجه لإدارة أموالك في مكان واحد
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {services.map((service) => {
              const IconComponent = service.icon
              return (
                <div key={service.id} className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 hover:shadow-xl transition-shadow">
                  <div className={`w-12 h-12 rounded-lg ${service.color} flex items-center justify-center mb-4`}>
                    <IconComponent className="w-6 h-6" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {service.title}
                  </h3>
                  <p className="text-gray-600 mb-4">
                    {service.description}
                  </p>
                  <ul className="space-y-2 mb-6">
                    {service.features.map((feature, index) => (
                      <li key={index} className="flex items-center text-sm text-gray-600">
                        <CheckCircleIcon className="w-4 h-4 text-green-500 ml-2" />
                        {feature}
                      </li>
                    ))}
                  </ul>
                  <Link 
                    href={service.href}
                    className="btn-primary w-full text-center"
                  >
                    استخدم الخدمة
                  </Link>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">لماذا تختار منصتنا؟</h2>
            <p className="text-lg text-gray-600">
              مميزات تجعلنا الخيار الأمثل لإدارة أموالك
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => {
              const IconComponent = feature.icon
              return (
                <div key={index} className="text-center">
                  <div className="w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <IconComponent className="w-8 h-8 text-primary-600" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">{feature.title}</h3>
                  <p className="text-gray-600">{feature.description}</p>
                </div>
              )
            })}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">ماذا يقول عملاؤنا</h2>
            <p className="text-lg text-gray-600">
              تجارب حقيقية من مستخدمين راضين عن خدماتنا
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <div key={index} className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                </div>
                <p className="text-gray-600 mb-4 italic">"{testimonial.content}"</p>
                <div>
                  <p className="font-semibold text-gray-900">{testimonial.name}</p>
                  <p className="text-sm text-gray-500">{testimonial.role}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-primary-600 py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-white mb-4">
            جاهز لبدء رحلتك المالية؟
          </h2>
          <p className="text-xl text-primary-100 mb-8">
            انضم إلى آلاف المستخدمين الذين يثقون بنا في إدارة أموالهم
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/login" className="bg-white text-primary-600 px-8 py-3 rounded-lg font-medium hover:bg-gray-100 transition-colors">
              ابدأ مجاناً
            </Link>
            <Link href="/contact" className="border-2 border-white text-white px-8 py-3 rounded-lg font-medium hover:bg-white hover:text-primary-600 transition-colors">
              تواصل معنا
            </Link>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  )
}