'use client'

import { useState } from 'react'
import { 
  ShieldCheckIcon, 
  KeyIcon, 
  EyeIcon,
  DocumentDuplicateIcon,
  Cog6ToothIcon
} from '@heroicons/react/24/outline'

interface Tab {
  id: string
  name: string
  icon: React.ComponentType<any>
  content: React.ReactNode
}

interface SecurityTabsProps {
  tabs: Tab[]
  defaultTab?: string
  className?: string
}

export default function SecurityTabs({ 
  tabs, 
  defaultTab,
  className = '' 
}: SecurityTabsProps) {
  const [activeTab, setActiveTab] = useState(defaultTab || tabs[0]?.id)

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content

  return (
    <div className={`w-full ${className}`}>
      {/* شريط التبويبات */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8 space-x-reverse" aria-label="Tabs">
          {tabs.map((tab) => {
            const IconComponent = tab.icon
            const isActive = activeTab === tab.id
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm
                  transition-colors duration-200
                  ${isActive
                    ? 'border-primary-500 text-primary-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
                aria-current={isActive ? 'page' : undefined}
              >
                <IconComponent
                  className={`
                    ml-2 -mr-0.5 h-5 w-5
                    ${isActive ? 'text-primary-500' : 'text-gray-400 group-hover:text-gray-500'}
                  `}
                  aria-hidden="true"
                />
                {tab.name}
              </button>
            )
          })}
        </nav>
      </div>

      {/* محتوى التبويب النشط */}
      <div className="mt-6">
        {activeTabContent}
      </div>
    </div>
  )
}

// مكونات التبويبات المحددة مسبقاً للأمان
export const SecurityTabsPreset = {
  // تبويب نظرة عامة
  Overview: {
    id: 'overview',
    name: 'نظرة عامة',
    icon: ShieldCheckIcon
  },
  
  // تبويب كلمة المرور
  Password: {
    id: 'password',
    name: 'كلمة المرور',
    icon: KeyIcon
  },
  
  // تبويب سجل الأنشطة
  ActivityLog: {
    id: 'activity',
    name: 'سجل الأنشطة',
    icon: EyeIcon
  },
  
  // تبويب النسخ الاحتياطية
  Backup: {
    id: 'backup',
    name: 'النسخ الاحتياطية',
    icon: DocumentDuplicateIcon
  },
  
  // تبويب الإعدادات المتقدمة
  Advanced: {
    id: 'advanced',
    name: 'إعدادات متقدمة',
    icon: Cog6ToothIcon
  }
}

// مكون مخصص لتبويبات الأمان
interface SecurityTabsWrapperProps {
  overviewContent: React.ReactNode
  passwordContent: React.ReactNode
  activityContent: React.ReactNode
  backupContent: React.ReactNode
  advancedContent?: React.ReactNode
  defaultTab?: string
  className?: string
}

export function SecurityTabsWrapper({
  overviewContent,
  passwordContent,
  activityContent,
  backupContent,
  advancedContent,
  defaultTab = 'overview',
  className = ''
}: SecurityTabsWrapperProps) {
  const tabs: Tab[] = [
    {
      ...SecurityTabsPreset.Overview,
      content: overviewContent
    },
    {
      ...SecurityTabsPreset.Password,
      content: passwordContent
    },
    {
      ...SecurityTabsPreset.ActivityLog,
      content: activityContent
    },
    {
      ...SecurityTabsPreset.Backup,
      content: backupContent
    }
  ]

  // إضافة تبويب الإعدادات المتقدمة إذا تم توفير المحتوى
  if (advancedContent) {
    tabs.push({
      ...SecurityTabsPreset.Advanced,
      content: advancedContent
    })
  }

  return (
    <SecurityTabs 
      tabs={tabs} 
      defaultTab={defaultTab}
      className={className}
    />
  )
}

// مكون مؤشر التبويب النشط
export function TabIndicator({ 
  isActive, 
  className = '' 
}: { 
  isActive: boolean
  className?: string 
}) {
  return (
    <div className={`
      absolute bottom-0 left-0 right-0 h-0.5 bg-primary-500 transform transition-transform duration-200
      ${isActive ? 'scale-x-100' : 'scale-x-0'}
      ${className}
    `} />
  )
}

// مكون عداد الإشعارات للتبويبات
export function TabBadge({ 
  count, 
  className = '',
  variant = 'default'
}: { 
  count: number
  className?: string
  variant?: 'default' | 'warning' | 'error'
}) {
  if (count === 0) return null

  const variantClasses = {
    default: 'bg-gray-100 text-gray-600',
    warning: 'bg-yellow-100 text-yellow-800',
    error: 'bg-red-100 text-red-800'
  }

  return (
    <span className={`
      inline-flex items-center justify-center px-2 py-1 mr-2 text-xs font-bold leading-none
      rounded-full ${variantClasses[variant]} ${className}
    `}>
      {count > 99 ? '99+' : count}
    </span>
  )
}
