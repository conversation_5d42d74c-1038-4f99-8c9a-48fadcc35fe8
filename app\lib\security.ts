/**
 * مكتبة الأمان والتشفير
 * تحتوي على جميع الوظائف الأمنية المطلوبة لحماية البيانات
 */

import CryptoJS from 'crypto-js'

// مفاتيح التشفير (في الإنتاج، يجب أن تكون من متغيرات البيئة)
const ENCRYPTION_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || 'financial-tracker-secret-key-2024'
const JWT_SECRET = process.env.NEXT_PUBLIC_JWT_SECRET || 'jwt-secret-key-2024'

// تشفير البيانات الحساسة
export const encryptData = (data: any): string => {
  try {
    const jsonString = JSON.stringify(data)
    const encrypted = CryptoJS.AES.encrypt(jsonString, ENCRYPTION_KEY).toString()
    return encrypted
  } catch (error) {
    console.error('خطأ في تشفير البيانات:', error)
    throw new Error('فشل في تشفير البيانات')
  }
}

// فك تشفير البيانات
export const decryptData = (encryptedData: string): any => {
  try {
    const decrypted = CryptoJS.AES.decrypt(encryptedData, ENCRYPTION_KEY)
    const jsonString = decrypted.toString(CryptoJS.enc.Utf8)
    return JSON.parse(jsonString)
  } catch (error) {
    console.error('خطأ في فك تشفير البيانات:', error)
    throw new Error('فشل في فك تشفير البيانات')
  }
}

// تشفير كلمة المرور
export const hashPassword = (password: string): string => {
  const salt = CryptoJS.lib.WordArray.random(128/8)
  const hash = CryptoJS.PBKDF2(password, salt, {
    keySize: 256/32,
    iterations: 10000
  })
  return salt.toString() + ':' + hash.toString()
}

// التحقق من كلمة المرور
export const verifyPassword = (password: string, hashedPassword: string): boolean => {
  try {
    const [salt, hash] = hashedPassword.split(':')
    const computedHash = CryptoJS.PBKDF2(password, CryptoJS.enc.Hex.parse(salt), {
      keySize: 256/32,
      iterations: 10000
    })
    return hash === computedHash.toString()
  } catch (error) {
    console.error('خطأ في التحقق من كلمة المرور:', error)
    return false
  }
}

// إنشاء JWT Token
export const generateJWT = (payload: any): string => {
  try {
    const header = {
      alg: 'HS256',
      typ: 'JWT'
    }
    
    const now = Math.floor(Date.now() / 1000)
    const jwtPayload = {
      ...payload,
      iat: now,
      exp: now + (24 * 60 * 60) // انتهاء الصلاحية بعد 24 ساعة
    }
    
    const encodedHeader = btoa(JSON.stringify(header))
    const encodedPayload = btoa(JSON.stringify(jwtPayload))
    const signature = CryptoJS.HmacSHA256(
      encodedHeader + '.' + encodedPayload,
      JWT_SECRET
    ).toString()
    
    return `${encodedHeader}.${encodedPayload}.${signature}`
  } catch (error) {
    console.error('خطأ في إنشاء JWT:', error)
    throw new Error('فشل في إنشاء رمز المصادقة')
  }
}

// التحقق من JWT Token
export const verifyJWT = (token: string): any => {
  try {
    const [encodedHeader, encodedPayload, signature] = token.split('.')
    
    // التحقق من التوقيع
    const expectedSignature = CryptoJS.HmacSHA256(
      encodedHeader + '.' + encodedPayload,
      JWT_SECRET
    ).toString()
    
    if (signature !== expectedSignature) {
      throw new Error('توقيع غير صحيح')
    }
    
    // فك تشفير البيانات
    const payload = JSON.parse(atob(encodedPayload))
    
    // التحقق من انتهاء الصلاحية
    const now = Math.floor(Date.now() / 1000)
    if (payload.exp && payload.exp < now) {
      throw new Error('انتهت صلاحية الرمز')
    }
    
    return payload
  } catch (error) {
    console.error('خطأ في التحقق من JWT:', error)
    return null
  }
}

// تنظيف المدخلات من XSS
export const sanitizeInput = (input: string): string => {
  if (typeof input !== 'string') return ''
  
  return input
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#x27;')
    .replace(/\//g, '&#x2F;')
    .trim()
}

// التحقق من قوة كلمة المرور
export const validatePasswordStrength = (password: string): {
  isValid: boolean
  score: number
  feedback: string[]
} => {
  const feedback: string[] = []
  let score = 0
  
  if (password.length >= 8) {
    score += 1
  } else {
    feedback.push('يجب أن تكون كلمة المرور 8 أحرف على الأقل')
  }
  
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    feedback.push('يجب أن تحتوي على حرف صغير')
  }
  
  if (/[A-Z]/.test(password)) {
    score += 1
  } else {
    feedback.push('يجب أن تحتوي على حرف كبير')
  }
  
  if (/[0-9]/.test(password)) {
    score += 1
  } else {
    feedback.push('يجب أن تحتوي على رقم')
  }
  
  if (/[^a-zA-Z0-9]/.test(password)) {
    score += 1
  } else {
    feedback.push('يجب أن تحتوي على رمز خاص')
  }
  
  return {
    isValid: score >= 4,
    score,
    feedback
  }
}

// إنشاء CSRF Token
export const generateCSRFToken = (): string => {
  return CryptoJS.lib.WordArray.random(32).toString()
}

// التحقق من CSRF Token
export const verifyCSRFToken = (token: string, storedToken: string): boolean => {
  return token === storedToken
}

// تسجيل الأنشطة الأمنية
export const logSecurityEvent = (event: {
  type: 'login' | 'logout' | 'failed_login' | 'password_change' | 'suspicious_activity'
  userId?: string
  ip?: string
  userAgent?: string
  details?: any
}) => {
  const logEntry = {
    ...event,
    timestamp: new Date().toISOString(),
    id: CryptoJS.lib.WordArray.random(16).toString()
  }
  
  // حفظ في التخزين المحلي (في الإنتاج، يُرسل للخادم)
  const existingLogs = JSON.parse(localStorage.getItem('security_logs') || '[]')
  existingLogs.push(logEntry)
  
  // الاحتفاظ بآخر 100 سجل فقط
  if (existingLogs.length > 100) {
    existingLogs.splice(0, existingLogs.length - 100)
  }
  
  localStorage.setItem('security_logs', JSON.stringify(existingLogs))
  console.log('🔒 تم تسجيل حدث أمني:', event.type)
}

// كشف الأنشطة المشبوهة
export const detectSuspiciousActivity = (activity: {
  type: string
  timestamp: Date
  ip?: string
  userAgent?: string
}): boolean => {
  // قواعد بسيطة لكشف الأنشطة المشبوهة
  const recentLogs = JSON.parse(localStorage.getItem('security_logs') || '[]')
  const lastHour = new Date(Date.now() - 60 * 60 * 1000)
  
  const recentFailedLogins = recentLogs.filter((log: any) => 
    log.type === 'failed_login' && 
    new Date(log.timestamp) > lastHour
  ).length
  
  // أكثر من 5 محاولات فاشلة في الساعة الواحدة
  if (activity.type === 'failed_login' && recentFailedLogins >= 5) {
    return true
  }
  
  return false
}

// تنظيف البيانات الحساسة من الذاكرة
export const clearSensitiveData = () => {
  // مسح المتغيرات الحساسة
  if (typeof window !== 'undefined') {
    // مسح كلمات المرور من النماذج
    const passwordInputs = document.querySelectorAll('input[type="password"]')
    passwordInputs.forEach((input: any) => {
      input.value = ''
    })
    
    // مسح البيانات الحساسة من sessionStorage
    sessionStorage.removeItem('temp_auth_data')
    sessionStorage.removeItem('csrf_token')
  }
}
