'use client'

import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'

export default function TestSavePage() {
  const { userData, addTransaction, updateProfile } = useData()
  const { success } = useToastContext()

  const testAddTransaction = () => {
    const testTransaction = {
      type: 'income' as const,
      title: 'راتب تجريبي',
      description: 'اختبار النظام',
      amount: 1000,
      date: new Date().toISOString().split('T')[0],
      time: new Date().toTimeString().slice(0, 5),
      status: 'completed' as const,
      category: 'راتب',
      reference: `TEST-${Date.now()}`,
      paymentMethod: 'تحويل بنكي'
    }
    
    addTransaction(testTransaction)
    success('تم إضافة معاملة تجريبية!')
  }

  const testUpdateProfile = () => {
    updateProfile({
      firstName: 'أحمد',
      lastName: 'محمد'
    })
    success('تم تحديث الملف الشخصي!')
  }

  const checkLocalStorage = () => {
    const transactions = localStorage.getItem('financialTracker_transactions')
    const profile = localStorage.getItem('financialTracker_profile')
    
    console.log('المعاملات في localStorage:', transactions)
    console.log('الملف الشخصي في localStorage:', profile)
    
    alert(`المعاملات: ${transactions ? 'موجودة' : 'غير موجودة'}\nالملف الشخصي: ${profile ? 'موجود' : 'غير موجود'}`)
  }

  return (
    <div className="space-y-8">
      <div>
        <h1 className="text-2xl font-bold text-gray-900">اختبار نظام الحفظ</h1>
        <p className="mt-1 text-sm text-gray-600">
          اختبر حفظ البيانات في localStorage
        </p>
      </div>

      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">أزرار الاختبار</h2>
        
        <div className="space-y-4">
          <button
            onClick={testAddTransaction}
            className="btn-primary w-full"
          >
            إضافة معاملة تجريبية
          </button>
          
          <button
            onClick={testUpdateProfile}
            className="btn-secondary w-full"
          >
            تحديث الملف الشخصي
          </button>
          
          <button
            onClick={checkLocalStorage}
            className="btn-secondary w-full"
          >
            فحص localStorage
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">البيانات الحالية</h2>
        
        <div className="space-y-4">
          <div>
            <h3 className="font-medium text-gray-900">الملف الشخصي:</h3>
            <pre className="text-sm bg-gray-100 p-2 rounded mt-2">
              {JSON.stringify(userData.profile, null, 2)}
            </pre>
          </div>
          
          <div>
            <h3 className="font-medium text-gray-900">المعاملات ({userData.transactions.length}):</h3>
            <pre className="text-sm bg-gray-100 p-2 rounded mt-2 max-h-40 overflow-y-auto">
              {JSON.stringify(userData.transactions, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  )
}