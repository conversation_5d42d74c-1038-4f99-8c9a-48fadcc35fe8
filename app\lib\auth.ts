/**
 * نظام المصادقة الآمن
 * يتعامل مع تسجيل الدخول، إنشاء الحسابات، وإدارة الجلسات
 */

import { 
  hashPassword, 
  verifyPassword, 
  generateJWT, 
  verifyJWT, 
  logSecurityEvent,
  detectSuspiciousActivity,
  sanitizeInput,
  validatePasswordStrength
} from './security'
import { encryptData, decryptData } from './security'

export interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  phone?: string
  createdAt: string
  lastLogin?: string
  isActive: boolean
  role: 'user' | 'admin'
  twoFactorEnabled?: boolean
}

export interface LoginCredentials {
  email: string
  password: string
  rememberMe?: boolean
}

export interface RegisterData {
  firstName: string
  lastName: string
  email: string
  phone?: string
  password: string
  confirmPassword: string
  agreeToTerms: boolean
}

export interface AuthSession {
  user: User
  token: string
  expiresAt: string
  csrfToken: string
}

// الحصول على المستخدمين المسجلين
const getStoredUsers = (): User[] => {
  if (typeof window === 'undefined') return []
  
  try {
    const encryptedUsers = localStorage.getItem('financial_tracker_users')
    if (!encryptedUsers) return []
    
    const users = decryptData(encryptedUsers)
    return Array.isArray(users) ? users : []
  } catch (error) {
    console.error('خطأ في قراءة بيانات المستخدمين:', error)
    return []
  }
}

// حفظ المستخدمين
const saveUsers = (users: User[]): boolean => {
  if (typeof window === 'undefined') return false
  
  try {
    const encryptedUsers = encryptData(users)
    localStorage.setItem('financial_tracker_users', encryptedUsers)
    return true
  } catch (error) {
    console.error('خطأ في حفظ بيانات المستخدمين:', error)
    return false
  }
}

// تسجيل مستخدم جديد
export const registerUser = async (userData: RegisterData): Promise<{
  success: boolean
  message: string
  user?: User
}> => {
  try {
    // تنظيف المدخلات
    const cleanData = {
      firstName: sanitizeInput(userData.firstName),
      lastName: sanitizeInput(userData.lastName),
      email: sanitizeInput(userData.email.toLowerCase()),
      phone: userData.phone ? sanitizeInput(userData.phone) : undefined,
      password: userData.password,
      confirmPassword: userData.confirmPassword,
      agreeToTerms: userData.agreeToTerms
    }

    // التحقق من البيانات
    if (!cleanData.firstName || !cleanData.lastName || !cleanData.email || !cleanData.password) {
      return { success: false, message: 'جميع الحقول مطلوبة' }
    }

    if (cleanData.password !== cleanData.confirmPassword) {
      return { success: false, message: 'كلمات المرور غير متطابقة' }
    }

    if (!cleanData.agreeToTerms) {
      return { success: false, message: 'يجب الموافقة على الشروط والأحكام' }
    }

    // التحقق من صحة البريد الإلكتروني
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(cleanData.email)) {
      return { success: false, message: 'البريد الإلكتروني غير صحيح' }
    }

    // التحقق من قوة كلمة المرور
    const passwordValidation = validatePasswordStrength(cleanData.password)
    if (!passwordValidation.isValid) {
      return { 
        success: false, 
        message: 'كلمة المرور ضعيفة: ' + passwordValidation.feedback.join(', ')
      }
    }

    // التحقق من عدم وجود المستخدم مسبقاً
    const existingUsers = getStoredUsers()
    const userExists = existingUsers.find(user => user.email === cleanData.email)
    
    if (userExists) {
      logSecurityEvent({
        type: 'failed_login',
        details: { reason: 'email_already_exists', email: cleanData.email }
      })
      return { success: false, message: 'البريد الإلكتروني مستخدم مسبقاً' }
    }

    // إنشاء المستخدم الجديد
    const hashedPassword = hashPassword(cleanData.password)
    const newUser: User = {
      id: generateUserId(),
      email: cleanData.email,
      firstName: cleanData.firstName,
      lastName: cleanData.lastName,
      phone: cleanData.phone,
      createdAt: new Date().toISOString(),
      isActive: true,
      role: 'user'
    }

    // حفظ كلمة المرور المشفرة منفصلة
    const userPasswords = JSON.parse(localStorage.getItem('financial_tracker_passwords') || '{}')
    userPasswords[newUser.id] = hashedPassword
    localStorage.setItem('financial_tracker_passwords', JSON.stringify(userPasswords))

    // حفظ المستخدم
    existingUsers.push(newUser)
    saveUsers(existingUsers)

    // تسجيل الحدث
    logSecurityEvent({
      type: 'login',
      userId: newUser.id,
      details: { action: 'user_registered' }
    })

    return { 
      success: true, 
      message: 'تم إنشاء الحساب بنجاح',
      user: newUser
    }

  } catch (error) {
    console.error('خطأ في تسجيل المستخدم:', error)
    return { success: false, message: 'حدث خطأ في إنشاء الحساب' }
  }
}

// تسجيل الدخول
export const loginUser = async (credentials: LoginCredentials): Promise<{
  success: boolean
  message: string
  session?: AuthSession
}> => {
  try {
    // تنظيف المدخلات
    const cleanCredentials = {
      email: sanitizeInput(credentials.email.toLowerCase()),
      password: credentials.password,
      rememberMe: credentials.rememberMe || false
    }

    // التحقق من البيانات
    if (!cleanCredentials.email || !cleanCredentials.password) {
      return { success: false, message: 'البريد الإلكتروني وكلمة المرور مطلوبان' }
    }

    // كشف الأنشطة المشبوهة
    const suspiciousActivity = detectSuspiciousActivity({
      type: 'login_attempt',
      timestamp: new Date()
    })

    if (suspiciousActivity) {
      logSecurityEvent({
        type: 'suspicious_activity',
        details: { reason: 'too_many_failed_attempts', email: cleanCredentials.email }
      })
      return { success: false, message: 'تم حظر الحساب مؤقتاً بسبب محاولات دخول مشبوهة' }
    }

    // البحث عن المستخدم
    const users = getStoredUsers()
    const user = users.find(u => u.email === cleanCredentials.email && u.isActive)

    if (!user) {
      logSecurityEvent({
        type: 'failed_login',
        details: { reason: 'user_not_found', email: cleanCredentials.email }
      })
      return { success: false, message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' }
    }

    // التحقق من كلمة المرور
    const userPasswords = JSON.parse(localStorage.getItem('financial_tracker_passwords') || '{}')
    const hashedPassword = userPasswords[user.id]

    if (!hashedPassword || !verifyPassword(cleanCredentials.password, hashedPassword)) {
      logSecurityEvent({
        type: 'failed_login',
        userId: user.id,
        details: { reason: 'invalid_password' }
      })
      return { success: false, message: 'البريد الإلكتروني أو كلمة المرور غير صحيحة' }
    }

    // تحديث آخر تسجيل دخول
    user.lastLogin = new Date().toISOString()
    const userIndex = users.findIndex(u => u.id === user.id)
    users[userIndex] = user
    saveUsers(users)

    // إنشاء الجلسة
    const token = generateJWT({ userId: user.id, email: user.email })
    const expiresAt = new Date(Date.now() + (cleanCredentials.rememberMe ? 30 : 1) * 24 * 60 * 60 * 1000)
    
    const session: AuthSession = {
      user,
      token,
      expiresAt: expiresAt.toISOString(),
      csrfToken: generateCSRFToken()
    }

    // حفظ الجلسة
    const encryptedSession = encryptData(session)
    localStorage.setItem('financial_tracker_session', encryptedSession)

    // تسجيل الحدث
    logSecurityEvent({
      type: 'login',
      userId: user.id,
      details: { rememberMe: cleanCredentials.rememberMe }
    })

    return { 
      success: true, 
      message: 'تم تسجيل الدخول بنجاح',
      session
    }

  } catch (error) {
    console.error('خطأ في تسجيل الدخول:', error)
    return { success: false, message: 'حدث خطأ في تسجيل الدخول' }
  }
}

// تسجيل الخروج
export const logoutUser = (): boolean => {
  try {
    const session = getCurrentSession()
    
    if (session) {
      logSecurityEvent({
        type: 'logout',
        userId: session.user.id
      })
    }

    // مسح الجلسة
    localStorage.removeItem('financial_tracker_session')
    sessionStorage.clear()
    
    return true
  } catch (error) {
    console.error('خطأ في تسجيل الخروج:', error)
    return false
  }
}

// الحصول على الجلسة الحالية
export const getCurrentSession = (): AuthSession | null => {
  if (typeof window === 'undefined') return null

  try {
    const encryptedSession = localStorage.getItem('financial_tracker_session')
    if (!encryptedSession) return null

    const session: AuthSession = decryptData(encryptedSession)
    
    // التحقق من انتهاء الصلاحية
    if (new Date(session.expiresAt) < new Date()) {
      localStorage.removeItem('financial_tracker_session')
      return null
    }

    // التحقق من صحة الرمز
    const tokenPayload = verifyJWT(session.token)
    if (!tokenPayload) {
      localStorage.removeItem('financial_tracker_session')
      return null
    }

    return session
  } catch (error) {
    console.error('خطأ في قراءة الجلسة:', error)
    localStorage.removeItem('financial_tracker_session')
    return null
  }
}

// التحقق من المصادقة
export const isAuthenticated = (): boolean => {
  return getCurrentSession() !== null
}

// إنشاء معرف مستخدم فريد
const generateUserId = (): string => {
  return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
}

// إنشاء CSRF Token
const generateCSRFToken = (): string => {
  return 'csrf_' + Date.now() + '_' + Math.random().toString(36).substr(2, 16)
}

// تغيير كلمة المرور
export const changePassword = async (
  currentPassword: string,
  newPassword: string
): Promise<{ success: boolean; message: string }> => {
  try {
    const session = getCurrentSession()
    if (!session) {
      return { success: false, message: 'يجب تسجيل الدخول أولاً' }
    }

    // الحصول على بيانات المستخدم المحفوظة
    const users = getStoredUsers()
    const userIndex = users.findIndex((u: any) => u.id === session.user.id)

    if (userIndex === -1) {
      return { success: false, message: 'المستخدم غير موجود' }
    }

    const user = users[userIndex]

    // الحصول على كلمة المرور المشفرة
    const userPasswords = JSON.parse(localStorage.getItem('financial_tracker_passwords') || '{}')
    const hashedCurrentPassword = userPasswords[user.id]

    if (!hashedCurrentPassword) {
      return { success: false, message: 'خطأ في النظام' }
    }

    // التحقق من كلمة المرور الحالية
    const isCurrentPasswordValid = verifyPassword(currentPassword, hashedCurrentPassword)
    if (!isCurrentPasswordValid) {
      logSecurityEvent({
        type: 'failed_login',
        userId: session.user.id,
        details: { reason: 'incorrect_current_password_for_change' }
      })
      return { success: false, message: 'كلمة المرور الحالية غير صحيحة' }
    }

    // التحقق من قوة كلمة المرور الجديدة
    const passwordValidation = validatePasswordStrength(newPassword)
    if (!passwordValidation.isValid) {
      return {
        success: false,
        message: 'كلمة المرور الجديدة ضعيفة: ' + passwordValidation.feedback.join(', ')
      }
    }

    // تشفير كلمة المرور الجديدة
    const hashedNewPassword = hashPassword(newPassword)

    // تحديث كلمة المرور
    userPasswords[user.id] = hashedNewPassword
    localStorage.setItem('financial_tracker_passwords', JSON.stringify(userPasswords))

    // تحديث تاريخ تغيير كلمة المرور
    users[userIndex].passwordChangedAt = new Date().toISOString()
    saveUsers(users)

    // تسجيل العملية
    logSecurityEvent({
      type: 'password_change',
      userId: session.user.id,
      details: { timestamp: new Date().toISOString() }
    })

    return { success: true, message: 'تم تغيير كلمة المرور بنجاح' }

  } catch (error) {
    console.error('خطأ في تغيير كلمة المرور:', error)
    return { success: false, message: 'فشل في تغيير كلمة المرور' }
  }
}
