// نظام حفظ البيانات الآمن في المتصفح
import { encryptData, decryptData } from './security'

export interface UserData {
  profile: {
    firstName: string
    lastName: string
    email: string
    phone: string
    birthDate: string
    address: string
    profilePicture?: string
  }
  transactions: Transaction[]
  invoices: Invoice[]
  requests: Request[]
  documents: Document[]
  settings: {
    notifications: {
      email: boolean
      sms: boolean
      push: boolean
      weeklyReport: boolean
      monthlyReport: boolean
      paymentReminders: boolean
      securityAlerts: boolean
    }
    security: {
      twoFactorEnabled: boolean
      passwordLastChanged: string
    }
    preferences: {
      language: string
      currency: string
      dateFormat: string
    }
  }
}

export interface Transaction {
  id: string
  type: 'payment' | 'income' | 'transfer' | 'refund'
  title: string
  description: string
  amount: number
  date: string
  time: string
  status: 'completed' | 'pending' | 'failed'
  category: string
  reference: string
  paymentMethod: string
}

export interface Invoice {
  id: string
  title: string
  company: string
  amount: number
  dueDate: string
  status: 'paid' | 'pending' | 'overdue'
  category: string
  description: string
  attachments: string[]
  createdAt: string
}

export interface Request {
  id: string
  type: string
  title: string
  description: string
  amount?: number
  status: 'pending' | 'approved' | 'rejected' | 'completed'
  requestDate: string
  processedDate?: string
  documents: string[]
  notes?: string
}

export interface Document {
  id: string
  title: string
  type: string
  category: string
  uploadDate: string
  size: number
  url?: string
  extractedData?: any
  tags: string[]
}

// الحصول على البيانات المشفرة من التخزين المحلي
export const getStoredData = (key: string): any => {
  if (typeof window === 'undefined') return null

  try {
    const item = localStorage.getItem(`financialTracker_${key}`)
    if (item) {
      // محاولة فك التشفير أولاً
      try {
        return decryptData(item)
      } catch (decryptError) {
        // إذا فشل فك التشفير، قد تكون البيانات غير مشفرة (للتوافق مع النسخة القديمة)
        console.warn(`البيانات غير مشفرة، سيتم تشفيرها في المرة القادمة: ${key}`)
        const plainData = JSON.parse(item)
        // إعادة حفظ البيانات مشفرة
        saveToStorage(key, plainData)
        return plainData
      }
    }
  } catch (error) {
    console.error(`خطأ في قراءة ${key} من التخزين المحلي:`, error)
  }
  return null
}

// حفظ البيانات المشفرة في التخزين المحلي
export const saveToStorage = (key: string, data: any): boolean => {
  if (typeof window === 'undefined') return false

  try {
    const encryptedData = encryptData(data)
    localStorage.setItem(`financialTracker_${key}`, encryptedData)
    console.log(`✅ تم حفظ ${key} بنجاح (مشفر)`)
    return true
  } catch (error) {
    console.error(`خطأ في حفظ ${key} في التخزين المحلي:`, error)
    return false
  }
}

// حذف البيانات من التخزين المحلي
export const removeFromStorage = (key: string): boolean => {
  if (typeof window === 'undefined') return false

  try {
    localStorage.removeItem(`financialTracker_${key}`)
    return true
  } catch (error) {
    console.error(`Error removing ${key} from localStorage:`, error)
    return false
  }
}

// تصدير جميع البيانات
export const exportAllData = (): string => {
  const userData: Partial<UserData> = {
    profile: getStoredData('profile'),
    transactions: getStoredData('transactions') || [],
    invoices: getStoredData('invoices') || [],
    requests: getStoredData('requests') || [],
    documents: getStoredData('documents') || [],
    settings: getStoredData('settings')
  }

  return JSON.stringify(userData, null, 2)
}

// استيراد البيانات
export const importData = (jsonData: string): boolean => {
  try {
    const userData: UserData = JSON.parse(jsonData)

    if (userData.profile) saveToStorage('profile', userData.profile)
    if (userData.transactions) saveToStorage('transactions', userData.transactions)
    if (userData.invoices) saveToStorage('invoices', userData.invoices)
    if (userData.requests) saveToStorage('requests', userData.requests)
    if (userData.documents) saveToStorage('documents', userData.documents)
    if (userData.settings) saveToStorage('settings', userData.settings)

    return true
  } catch (error) {
    console.error('Error importing data:', error)
    return false
  }
}

// البيانات الافتراضية - فارغة للمستخدم الجديد
export const getDefaultUserData = (): UserData => ({
  profile: {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    birthDate: '',
    address: ''
  },
  transactions: [],
  invoices: [],
  requests: [],
  documents: [],
  settings: {
    notifications: {
      email: true,
      sms: false,
      push: true,
      weeklyReport: false,
      monthlyReport: true,
      paymentReminders: true,
      securityAlerts: true,
    },
    security: {
      twoFactorEnabled: false,
      passwordLastChanged: '',
    },
    preferences: {
      language: 'ar',
      currency: 'SAR',
      dateFormat: 'DD/MM/YYYY',
    },
  },
})

// حفظ تلقائي مع debounce
let saveTimeout: NodeJS.Timeout | null = null

export const saveWithDebounce = (key: string, data: any, delay: number = 1000) => {
  if (saveTimeout) {
    clearTimeout(saveTimeout)
  }
  
  saveTimeout = setTimeout(() => {
    saveToStorage(key, data)
  }, delay)
}