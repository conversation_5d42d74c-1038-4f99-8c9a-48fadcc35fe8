'use client'

import { useState } from 'react'
import Link from 'next/link'
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CurrencyDollarIcon,
  CalendarIcon,
  DocumentChartBarIcon
} from '@heroicons/react/24/outline'

const monthlyData: any[] = []
const expenseCategories: any[] = []

export default function ReportsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState('6months')

  const totalIncome = 0
  const totalExpenses = 0
  const totalSavings = 0

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">التقارير المالية</h1>
          <p className="mt-1 text-sm text-gray-600">
            تحليل مفصل لوضعك المالي ونمط الإنفاق
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <select
            value={selectedPeriod}
            onChange={(e) => setSelectedPeriod(e.target.value)}
            className="input-field w-auto"
          >
            <option value="1month">الشهر الماضي</option>
            <option value="3months">آخر 3 أشهر</option>
            <option value="6months">آخر 6 أشهر</option>
            <option value="1year">آخر سنة</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center ml-4">
              <ArrowTrendingUpIcon className="w-6 h-6 text-green-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الدخل</p>
              <p className="text-2xl font-bold text-green-600">{totalIncome} ر.س</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center ml-4">
              <ArrowTrendingDownIcon className="w-6 h-6 text-red-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
              <p className="text-2xl font-bold text-red-600">{totalExpenses} ر.س</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center ml-4">
              <CurrencyDollarIcon className="w-6 h-6 text-blue-600" />
            </div>
            <div>
              <p className="text-sm font-medium text-gray-600">صافي الادخار</p>
              <p className="text-2xl font-bold text-blue-600">{totalSavings} ر.س</p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Monthly Chart */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <ChartBarIcon className="w-5 h-5 ml-2" />
              الاتجاه الشهري
            </h2>
          </div>
          <div className="p-6">
            <div className="text-center py-12">
              <ChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد بيانات للتحليل</h3>
              <p className="text-gray-500 mb-4">
                ابدأ بإضافة معاملاتك المالية لرؤية التقارير هنا
              </p>
              <Link href="/dashboard/history" className="btn-primary">
                إضافة معاملة
              </Link>
            </div>
          </div>
        </div>

        {/* Expense Categories */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900 flex items-center">
              <DocumentChartBarIcon className="w-5 h-5 ml-2" />
              تصنيف المصروفات
            </h2>
          </div>
          <div className="p-6">
            <div className="text-center py-12">
              <DocumentChartBarIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">لا توجد مصروفات للتصنيف</h3>
              <p className="text-gray-500">
                ستظهر هنا تصنيفات مصروفاتك عند إضافة معاملات
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Insights */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">تحليل ذكي</h2>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <h3 className="font-medium text-green-800 mb-2">نقاط إيجابية</h3>
              <ul className="text-sm text-green-700 space-y-1">
                <li>• معدل الادخار الشهري في تحسن مستمر</li>
                <li>• انخفاض مصروفات المواصلات بنسبة 15%</li>
                <li>• الالتزام بالميزانية المحددة</li>
              </ul>
            </div>
            
            <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
              <h3 className="font-medium text-orange-800 mb-2">توصيات للتحسين</h3>
              <ul className="text-sm text-orange-700 space-y-1">
                <li>• يمكن تقليل مصروفات الترفيه بنسبة 20%</li>
                <li>• البحث عن بدائل أوفر للمصروفات الثابتة</li>
                <li>• تحديد هدف ادخار أعلى للشهر القادم</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}