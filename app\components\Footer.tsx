import Link from 'next/link'
import { 
  EnvelopeIcon, 
  PhoneIcon, 
  MapPinIcon 
} from '@heroicons/react/24/outline'

export default function Footer() {
  const currentYear = new Date().getFullYear()

  const footerLinks = {
    services: [
      { name: 'إدارة الفواتير', href: '/dashboard/invoices' },
      { name: 'التقارير المالية', href: '/dashboard/reports' },
      { name: 'الطلبات المالية', href: '/dashboard/requests' },
      { name: 'إدارة المستندات', href: '/dashboard/documents' }
    ],
    support: [
      { name: 'مركز المساعدة', href: '/help' },
      { name: 'الأسئلة الشائعة', href: '/faq' },
      { name: 'تواصل معنا', href: '/contact' },
      { name: 'الدعم الفني', href: '/support' }
    ],
    legal: [
      { name: 'سياسة الخصوصية', href: '/privacy' },
      { name: 'شروط الاستخدام', href: '/terms' },
      { name: 'سياسة الأمان', href: '/security' },
      { name: 'إخلاء المسؤولية', href: '/disclaimer' }
    ]
  }

  return (
    <footer className="bg-gradient-to-br from-dark-50 via-dark-100 to-dark-200 text-white border-t border-dark-300">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-1">
            <div className="flex items-center space-x-2 space-x-reverse mb-4">
              <div className="w-8 h-8 bg-gradient-to-r from-electric-600 to-electric-700 rounded-lg flex items-center justify-center shadow-lg shadow-electric-600/25">
                <span className="text-white font-bold text-sm">م</span>
              </div>
              <span className="text-xl font-bold glow-text">متابعة مالية</span>
            </div>
            <p className="text-gray-400 text-sm mb-6">
              منصتك الذكية للإدارة المالية. نساعدك في متابعة وإدارة وضعك المالي بدقة وأمان عالي.
            </p>
            <div className="space-y-2">
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-400">
                <PhoneIcon className="w-4 h-4" />
                <span>+966 11 123 4567</span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-400">
                <EnvelopeIcon className="w-4 h-4" />
                <span><EMAIL></span>
              </div>
              <div className="flex items-center space-x-2 space-x-reverse text-sm text-gray-400">
                <MapPinIcon className="w-4 h-4" />
                <span>الرياض، المملكة العربية السعودية</span>
              </div>
            </div>
          </div>

          {/* Services */}
          <div>
            <h3 className="text-lg font-semibold mb-4">الخدمات</h3>
            <ul className="space-y-2">
              {footerLinks.services.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href}
                    className="text-gray-400 hover:text-white text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-lg font-semibold mb-4">الدعم</h3>
            <ul className="space-y-2">
              {footerLinks.support.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href}
                    className="text-gray-400 hover:text-white text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Legal */}
          <div>
            <h3 className="text-lg font-semibold mb-4">قانوني</h3>
            <ul className="space-y-2">
              {footerLinks.legal.map((link) => (
                <li key={link.name}>
                  <Link 
                    href={link.href}
                    className="text-gray-400 hover:text-white text-sm transition-colors"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-800 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © {currentYear} متابعة مالية. جميع الحقوق محفوظة.
            </p>
            <div className="flex space-x-6 space-x-reverse mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">مطور بـ ❤️ للمجتمع العربي</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}