'use client'

import { useState } from 'react'
import { useData } from '@/contexts/DataContext'
import { useToastContext } from '@/contexts/ToastContext'
import AddTransactionModal from '@/components/AddTransactionModal'
import {
  ClockIcon,
  CheckCircleIcon,
  XCircleIcon,
  ExclamationTriangleIcon,
  EyeIcon,
  ArrowPathIcon,
  CalendarIcon,
  PlusIcon,
  DocumentTextIcon,
  FunnelIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

const transactionTypes = {
  payment: { label: 'دفع', color: 'text-red-600 bg-red-50' },
  income: { label: 'دخل', color: 'text-green-600 bg-green-50' },
  transfer: { label: 'تحويل', color: 'text-blue-600 bg-blue-50' },
  refund: { label: 'استرداد', color: 'text-purple-600 bg-purple-50' }
}

const statusTypes = {
  completed: { label: 'مكتمل', color: 'text-green-600 bg-green-50', icon: CheckCircleIcon },
  pending: { label: 'معلق', color: 'text-yellow-600 bg-yellow-50', icon: ExclamationTriangleIcon },
  failed: { label: 'فاشل', color: 'text-red-600 bg-red-50', icon: XCircleIcon }
}

export default function HistoryPage() {
  const { userData, isLoading, deleteTransaction } = useData()
  const { success, error } = useToastContext()
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedType, setSelectedType] = useState('all')
  const [selectedStatus, setSelectedStatus] = useState('all')
  const [dateRange, setDateRange] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)

  // استخدام البيانات المحفوظة
  const transactions = userData?.transactions || []

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = (transaction.title?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (transaction.description?.toLowerCase() || '').includes(searchTerm.toLowerCase()) ||
                         (transaction.reference?.toLowerCase() || '').includes(searchTerm.toLowerCase())
    const matchesType = selectedType === 'all' || transaction.type === selectedType
    const matchesStatus = selectedStatus === 'all' || transaction.status === selectedStatus
    
    let matchesDate = true
    if (dateRange !== 'all') {
      const transactionDate = new Date(transaction.date)
      const now = new Date()
      const daysAgo = {
        '7': 7,
        '30': 30,
        '90': 90
      }[dateRange]
      
      if (daysAgo) {
        const cutoffDate = new Date(now.getTime() - (daysAgo * 24 * 60 * 60 * 1000))
        matchesDate = transactionDate >= cutoffDate
      }
    }
    
    return matchesSearch && matchesType && matchesStatus && matchesDate
  })

  const getTransactionStats = () => {
    const completed = transactions.filter(t => t.status === 'completed')
    const totalIncome = completed.filter(t => t.amount > 0).reduce((sum, t) => sum + t.amount, 0)
    const totalExpenses = completed.filter(t => t.amount < 0).reduce((sum, t) => sum + Math.abs(t.amount), 0)
    
    return {
      total: transactions.length,
      completed: completed.length,
      pending: transactions.filter(t => t.status === 'pending').length,
      failed: transactions.filter(t => t.status === 'failed').length,
      totalIncome,
      totalExpenses,
      netAmount: totalIncome - totalExpenses
    }
  }

  const handleDeleteTransaction = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه المعاملة؟')) {
      deleteTransaction(id)
      success('تم حذف المعاملة بنجاح')
    }
  }

  const stats = getTransactionStats()

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <ArrowPathIcon className="w-8 h-8 animate-spin text-primary-600 mx-auto mb-2" />
          <p className="text-gray-600">جاري تحميل البيانات...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">سجل العمليات</h1>
          <p className="mt-1 text-sm text-gray-600">
            تتبع جميع عملياتك المالية والمعاملات السابقة
          </p>
        </div>
        <button
          onClick={() => setShowAddModal(true)}
          className="btn-primary flex items-center space-x-2 space-x-reverse"
        >
          <PlusIcon className="w-5 h-5" />
          <span>إضافة معاملة</span>
        </button>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المعاملات</p>
              <p className="text-2xl font-bold text-gray-900 mt-1">{stats.total}</p>
            </div>
            <div className="p-3 bg-blue-50 rounded-full">
              <DocumentTextIcon className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي الدخل</p>
              <p className="text-2xl font-bold text-green-600 mt-1">
                {stats.totalIncome.toLocaleString('ar-SA')} ر.س
              </p>
            </div>
            <div className="p-3 bg-green-50 rounded-full">
              <CheckCircleIcon className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">إجمالي المصروفات</p>
              <p className="text-2xl font-bold text-red-600 mt-1">
                {stats.totalExpenses.toLocaleString('ar-SA')} ر.س
              </p>
            </div>
            <div className="p-3 bg-red-50 rounded-full">
              <XCircleIcon className="w-6 h-6 text-red-600" />
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md p-6 border border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">الصافي</p>
              <p className={`text-2xl font-bold mt-1 ${stats.netAmount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {stats.netAmount.toLocaleString('ar-SA')} ر.س
              </p>
            </div>
            <div className={`p-3 rounded-full ${stats.netAmount >= 0 ? 'bg-green-50' : 'bg-red-50'}`}>
              <ClockIcon className={`w-6 h-6 ${stats.netAmount >= 0 ? 'text-green-600' : 'text-red-600'}`} />
            </div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <div className="flex flex-wrap gap-4">
          {/* Search */}
          <div className="flex-1 min-w-64">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
              <input
                type="text"
                placeholder="البحث في المعاملات..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="input-field pr-10"
              />
            </div>
          </div>

          {/* Type Filter */}
          <select
            value={selectedType}
            onChange={(e) => setSelectedType(e.target.value)}
            className="input-field min-w-32"
          >
            <option value="all">جميع الأنواع</option>
            <option value="payment">دفع</option>
            <option value="income">دخل</option>
            <option value="transfer">تحويل</option>
            <option value="refund">استرداد</option>
          </select>

          {/* Status Filter */}
          <select
            value={selectedStatus}
            onChange={(e) => setSelectedStatus(e.target.value)}
            className="input-field min-w-32"
          >
            <option value="all">جميع الحالات</option>
            <option value="completed">مكتمل</option>
            <option value="pending">معلق</option>
            <option value="failed">فاشل</option>
          </select>

          {/* Date Range Filter */}
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="input-field min-w-32"
          >
            <option value="all">جميع التواريخ</option>
            <option value="7">آخر 7 أيام</option>
            <option value="30">آخر 30 يوم</option>
            <option value="90">آخر 90 يوم</option>
          </select>
        </div>
      </div>

      {/* Transactions Table */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h2 className="text-lg font-semibold text-gray-900">
            المعاملات ({filteredTransactions.length})
          </h2>
        </div>
        
        {filteredTransactions.length === 0 ? (
          <div className="p-12 text-center">
            <DocumentTextIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
            <p className="text-gray-500 text-lg mb-2">لا توجد معاملات</p>
            <p className="text-gray-400 text-sm">
              {searchTerm || selectedType !== 'all' || selectedStatus !== 'all' || dateRange !== 'all'
                ? 'لا توجد معاملات تطابق المرشحات المحددة'
                : 'لم تقم بإجراء أي معاملات بعد'}
            </p>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المعاملة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    النوع
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    المبلغ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    التاريخ
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الحالة
                  </th>
                  <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    الإجراءات
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredTransactions.map((transaction) => {
                  const typeConfig = transactionTypes[transaction.type as keyof typeof transactionTypes]
                  const statusConfig = statusTypes[transaction.status as keyof typeof statusTypes]
                  const StatusIcon = statusConfig?.icon

                  return (
                    <tr key={transaction.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {transaction.title}
                          </div>
                          <div className="text-sm text-gray-500">
                            {transaction.description}
                          </div>
                          <div className="text-xs text-gray-400">
                            {transaction.reference}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${typeConfig?.color || 'text-gray-600 bg-gray-50'}`}>
                          {typeConfig?.label || transaction.type}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`text-sm font-semibold ${transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {transaction.amount >= 0 ? '+' : ''}{transaction.amount.toLocaleString('ar-SA')} ر.س
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div>{new Date(transaction.date).toLocaleDateString('ar-SA')}</div>
                        <div className="text-xs text-gray-500">{transaction.time}</div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2 py-1 text-xs font-semibold rounded-full ${statusConfig?.color || 'text-gray-600 bg-gray-50'}`}>
                          {StatusIcon && <StatusIcon className="w-3 h-3 ml-1" />}
                          {statusConfig?.label || transaction.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex items-center space-x-2 space-x-reverse">
                          <button className="text-primary-600 hover:text-primary-900">
                            <EyeIcon className="w-4 h-4" />
                          </button>
                          <button 
                            onClick={() => handleDeleteTransaction(transaction.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <XCircleIcon className="w-4 h-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  )
                })}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add Transaction Modal */}
      <AddTransactionModal 
        isOpen={showAddModal} 
        onClose={() => setShowAddModal(false)} 
      />

      {/* Export Options */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">تصدير البيانات</h2>
        <div className="flex flex-wrap gap-3">
          <button className="btn-primary flex items-center space-x-2 space-x-reverse">
            <DocumentTextIcon className="w-4 h-4" />
            <span>تصدير PDF</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2 space-x-reverse">
            <DocumentTextIcon className="w-4 h-4" />
            <span>تصدير Excel</span>
          </button>
          <button className="btn-secondary flex items-center space-x-2 space-x-reverse">
            <DocumentTextIcon className="w-4 h-4" />
            <span>تصدير CSV</span>
          </button>
        </div>
      </div>
    </div>
  )
}