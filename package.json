{"name": "financial-tracker", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "export": "next build", "deploy": "npm run export && gh-pages -d out"}, "dependencies": {"@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@types/crypto-js": "^4.2.2", "clsx": "^2.0.0", "crypto-js": "^4.2.0", "lucide-react": "^0.292.0", "next": "^15.3.4", "react": "^18", "react-dom": "^18"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "eslint": "^8", "eslint-config-next": "14.0.3", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}