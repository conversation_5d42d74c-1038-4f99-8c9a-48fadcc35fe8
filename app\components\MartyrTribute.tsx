'use client'

import { useState, useEffect } from 'react'
import { HeartIcon, StarIcon } from '@heroicons/react/24/solid'

interface MartyrTributeProps {
  variant?: 'header' | 'footer' | 'sidebar' | 'floating'
  className?: string
}

export default function MartyrTribute({ variant = 'header', className = '' }: MartyrTributeProps) {
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  const renderHeader = () => (
    <div className={`flex items-center justify-center space-x-2 space-x-reverse ${className}`}>
      <div className="flex items-center space-x-1 space-x-reverse">
        <StarIcon className="w-4 h-4 text-gold-500 animate-pulse-slow" />
        <span className="text-sm font-medium text-gray-700">
          مؤسس هذا المشروع
        </span>
        <StarIcon className="w-4 h-4 text-gold-500 animate-pulse-slow" />
      </div>
      <div className="flex items-center space-x-1 space-x-reverse">
        <HeartIcon className="w-4 h-4 text-neon-500 animate-martyr-glow" />
        <span className="text-sm font-bold text-neon-700 animate-fade-in">
          الشهيد موسى سلطان
        </span>
        <HeartIcon className="w-4 h-4 text-neon-500 animate-martyr-glow" />
      </div>
      <span className="text-xs text-gray-500">رحمه الله</span>
    </div>
  )

  const renderFooter = () => (
    <div className={`text-center py-4 border-t border-neon-200 bg-gradient-to-r from-neon-50 to-gold-50 ${className}`}>
      <div className="flex items-center justify-center space-x-3 space-x-reverse mb-2">
        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-neon-400 to-gold-400 flex items-center justify-center animate-glow">
          <HeartIcon className="w-4 h-4 text-white" />
        </div>
        <div className="text-center">
          <p className="text-sm font-bold text-gray-800">
            مؤسس هذا المشروع: <span className="text-neon-700">الشهيد موسى سلطان</span>
          </p>
          <p className="text-xs text-gray-600">رحمه الله وأسكنه فسيح جناته</p>
        </div>
        <div className="w-8 h-8 rounded-full bg-gradient-to-r from-gold-400 to-neon-400 flex items-center justify-center animate-glow">
          <StarIcon className="w-4 h-4 text-white" />
        </div>
      </div>
      <div className="text-xs text-gray-500">
        "وَلَا تَحْسَبَنَّ الَّذِينَ قُتِلُوا فِي سَبِيلِ اللَّهِ أَمْوَاتًا ۚ بَلْ أَحْيَاءٌ عِندَ رَبِّهِمْ يُرْزَقُونَ"
      </div>
    </div>
  )

  const renderSidebar = () => (
    <div className={`p-4 rounded-lg bg-gradient-to-br from-neon-50 via-white to-gold-50 border border-neon-200 shadow-martyr ${className}`}>
      <div className="text-center">
        <div className="w-12 h-12 mx-auto mb-3 rounded-full bg-gradient-to-r from-neon-400 to-gold-400 flex items-center justify-center animate-martyr-glow">
          <HeartIcon className="w-6 h-6 text-white" />
        </div>
        <h3 className="text-sm font-bold text-gray-800 mb-1">مؤسس المشروع</h3>
        <p className="text-lg font-bold text-neon-700 mb-1">الشهيد موسى سلطان</p>
        <p className="text-xs text-gray-600 mb-2">رحمه الله</p>
        <div className="flex items-center justify-center space-x-1 space-x-reverse">
          <StarIcon className="w-3 h-3 text-gold-500" />
          <StarIcon className="w-3 h-3 text-gold-500" />
          <StarIcon className="w-3 h-3 text-gold-500" />
        </div>
      </div>
    </div>
  )

  const renderFloating = () => (
    <div className={`fixed bottom-4 right-4 z-50 ${isVisible ? 'animate-slide-up' : 'opacity-0'} ${className}`}>
      <div className="bg-white rounded-full p-3 shadow-martyr border border-neon-200 hover:shadow-glow-lg transition-all duration-300 cursor-pointer group">
        <div className="flex items-center space-x-2 space-x-reverse">
          <HeartIcon className="w-5 h-5 text-neon-500 group-hover:animate-pulse" />
          <span className="text-xs font-medium text-gray-700 hidden group-hover:block animate-fade-in">
            الشهيد موسى سلطان
          </span>
        </div>
      </div>
    </div>
  )

  switch (variant) {
    case 'header':
      return renderHeader()
    case 'footer':
      return renderFooter()
    case 'sidebar':
      return renderSidebar()
    case 'floating':
      return renderFloating()
    default:
      return renderHeader()
  }
}

// مكون خاص للصفحة الرئيسية
export function MartyrHeroSection() {
  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-dark-50 via-dark-100 to-dark-200 py-16">
      {/* خلفية متحركة */}
      <div className="absolute inset-0 bg-black-hero opacity-40"></div>
      <div className="absolute inset-0 bg-dark-grid bg-grid opacity-30"></div>

      {/* المحتوى */}
      <div className="relative max-w-4xl mx-auto text-center px-4">
        <div className="mb-8">
          <div className="inline-flex items-center justify-center w-20 h-20 rounded-full bg-gradient-to-r from-electric-400 to-neon-400 mb-6 animate-float shadow-lg shadow-electric-400/25">
            <HeartIcon className="w-10 h-10 text-white" />
          </div>

          <h1 className="text-4xl md:text-6xl font-bold mb-4 animate-fade-in">
            <span className="bg-gradient-to-r from-electric-400 to-neon-400 bg-clip-text text-transparent glow-text">
              مشروع الشهيد
            </span>
          </h1>

          <h2 className="text-3xl md:text-5xl font-bold text-white mb-6 animate-slide-up glow-text">
            موسى سلطان
          </h2>
          
          <p className="text-lg text-gray-300 mb-8 max-w-2xl mx-auto animate-fade-in">
            منصة مالية متطورة تحمل اسم وذكرى الشهيد موسى سلطان، رحمه الله وأسكنه فسيح جناته.
            مشروع يهدف إلى تسهيل إدارة الأمور المالية بأحدث التقنيات والأمان.
          </p>
          
          <div className="flex items-center justify-center space-x-4 space-x-reverse mb-8">
            <div className="flex items-center space-x-1 space-x-reverse animate-glow">
              <StarIcon className="w-5 h-5 text-gold-500" />
              <StarIcon className="w-5 h-5 text-gold-500" />
              <StarIcon className="w-5 h-5 text-gold-500" />
              <StarIcon className="w-5 h-5 text-gold-500" />
              <StarIcon className="w-5 h-5 text-gold-500" />
            </div>
          </div>
          
          <div className="bg-white/80 backdrop-blur-sm rounded-lg p-6 border border-neon-200 shadow-martyr max-w-2xl mx-auto animate-scale-in">
            <p className="text-sm text-gray-700 italic">
              "وَلَا تَحْسَبَنَّ الَّذِينَ قُتِلُوا فِي سَبِيلِ اللَّهِ أَمْوَاتًا ۚ بَلْ أَحْيَاءٌ عِندَ رَبِّهِمْ يُرْزَقُونَ"
            </p>
            <p className="text-xs text-gray-500 mt-2">سورة آل عمران - الآية 169</p>
          </div>
        </div>
      </div>
    </div>
  )
}
