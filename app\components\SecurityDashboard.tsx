'use client'

import { useState, useEffect } from 'react'
import { 
  ShieldCheckIcon, 
  KeyIcon, 
  EyeIcon,
  ClockIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  LockClosedIcon,
  UserIcon,
  DevicePhoneMobileIcon,
  GlobeAltIcon
} from '@heroicons/react/24/outline'
import { getCurrentSession } from '@/lib/auth'

interface SecurityMetrics {
  overallScore: number
  lastLogin: string
  failedAttempts: number
  dataEncrypted: boolean
  backupCount: number
  passwordStrength: number
  sessionCount: number
  suspiciousActivities: number
  lastPasswordChange: string
  twoFactorEnabled: boolean
}

interface SecurityRecommendation {
  id: string
  title: string
  description: string
  priority: 'high' | 'medium' | 'low'
  completed: boolean
  action?: string
}

export default function SecurityDashboard() {
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null)
  const [recommendations, setRecommendations] = useState<SecurityRecommendation[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadSecurityMetrics()
  }, [])

  const loadSecurityMetrics = () => {
    try {
      const session = getCurrentSession()
      if (!session) return

      // تحميل البيانات الأمنية
      const logs = JSON.parse(localStorage.getItem('security_logs') || '[]')
      const backups = JSON.parse(localStorage.getItem('financial_tracker_backups') || '[]')
      const users = JSON.parse(localStorage.getItem('registered_users') || '[]')
      
      const currentUser = users.find((u: any) => u.id === session.user.id)
      
      const loginLogs = logs.filter((log: any) => log.type === 'login')
      const failedLogins = logs.filter((log: any) => log.type === 'failed_login')
      const suspiciousLogs = logs.filter((log: any) => log.type === 'suspicious_activity')
      const passwordChanges = logs.filter((log: any) => log.type === 'password_change')

      const securityMetrics: SecurityMetrics = {
        overallScore: calculateOverallScore(logs, backups, currentUser),
        lastLogin: loginLogs.length > 0 ? loginLogs[loginLogs.length - 1].timestamp : 'غير متوفر',
        failedAttempts: failedLogins.length,
        dataEncrypted: true,
        backupCount: backups.length,
        passwordStrength: calculatePasswordStrength(currentUser),
        sessionCount: 1, // الجلسة الحالية
        suspiciousActivities: suspiciousLogs.length,
        lastPasswordChange: passwordChanges.length > 0 ? 
          passwordChanges[passwordChanges.length - 1].timestamp : 
          currentUser?.createdAt || 'غير متوفر',
        twoFactorEnabled: false // لم يتم تطبيقه بعد
      }

      setMetrics(securityMetrics)
      setRecommendations(generateRecommendations(securityMetrics))
      setLoading(false)
    } catch (error) {
      console.error('خطأ في تحميل البيانات الأمنية:', error)
      setLoading(false)
    }
  }

  const calculateOverallScore = (logs: any[], backups: any[], user: any): number => {
    let score = 40 // نقطة البداية

    // النسخ الاحتياطية (+30 نقطة كحد أقصى)
    score += Math.min(backups.length * 10, 30)

    // عدم وجود محاولات فاشلة (+15 نقطة)
    const failedLogins = logs.filter(log => log.type === 'failed_login')
    if (failedLogins.length === 0) score += 15

    // عدم وجود أنشطة مشبوهة (+10 نقاط)
    const suspiciousActivities = logs.filter(log => log.type === 'suspicious_activity')
    if (suspiciousActivities.length === 0) score += 10

    // تغيير كلمة المرور مؤخراً (+5 نقاط)
    const passwordChanges = logs.filter(log => log.type === 'password_change')
    if (passwordChanges.length > 0) {
      const lastChange = new Date(passwordChanges[passwordChanges.length - 1].timestamp)
      const daysSinceChange = (Date.now() - lastChange.getTime()) / (1000 * 60 * 60 * 24)
      if (daysSinceChange < 90) score += 5
    }

    // خصم نقاط للأنشطة المشبوهة
    score -= suspiciousActivities.length * 3

    return Math.max(0, Math.min(100, score))
  }

  const calculatePasswordStrength = (user: any): number => {
    // هذا تقدير بسيط - في التطبيق الحقيقي نحتاج لتحليل كلمة المرور
    return 75 // افتراض أن كلمة المرور قوية نسبياً
  }

  const generateRecommendations = (metrics: SecurityMetrics): SecurityRecommendation[] => {
    const recommendations: SecurityRecommendation[] = []

    if (metrics.backupCount === 0) {
      recommendations.push({
        id: 'backup',
        title: 'إنشاء نسخة احتياطية',
        description: 'قم بإنشاء نسخة احتياطية من بياناتك المالية لحمايتها من الفقدان',
        priority: 'high',
        completed: false,
        action: 'إنشاء نسخة احتياطية'
      })
    }

    if (metrics.failedAttempts > 3) {
      recommendations.push({
        id: 'failed_attempts',
        title: 'مراجعة محاولات الدخول الفاشلة',
        description: 'تم رصد محاولات دخول فاشلة متعددة. تأكد من أمان حسابك',
        priority: 'high',
        completed: false,
        action: 'مراجعة السجل'
      })
    }

    if (!metrics.twoFactorEnabled) {
      recommendations.push({
        id: 'two_factor',
        title: 'تفعيل المصادقة الثنائية',
        description: 'أضف طبقة حماية إضافية لحسابك باستخدام المصادقة الثنائية',
        priority: 'medium',
        completed: false,
        action: 'تفعيل المصادقة الثنائية'
      })
    }

    const lastPasswordChange = new Date(metrics.lastPasswordChange)
    const daysSinceChange = (Date.now() - lastPasswordChange.getTime()) / (1000 * 60 * 60 * 24)
    
    if (daysSinceChange > 90) {
      recommendations.push({
        id: 'password_change',
        title: 'تغيير كلمة المرور',
        description: 'لم يتم تغيير كلمة المرور منذ أكثر من 90 يوماً',
        priority: 'medium',
        completed: false,
        action: 'تغيير كلمة المرور'
      })
    }

    if (metrics.suspiciousActivities > 0) {
      recommendations.push({
        id: 'suspicious',
        title: 'مراجعة الأنشطة المشبوهة',
        description: 'تم رصد أنشطة مشبوهة في حسابك. يُنصح بمراجعتها',
        priority: 'high',
        completed: false,
        action: 'مراجعة الأنشطة'
      })
    }

    return recommendations
  }

  const getScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getScoreLabel = (score: number): string => {
    if (score >= 80) return 'ممتاز'
    if (score >= 60) return 'جيد'
    return 'يحتاج تحسين'
  }

  const getPriorityColor = (priority: string): string => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="bg-white rounded-lg shadow-sm border p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-8 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (!metrics) return null

  return (
    <div className="space-y-8">
      {/* إحصائيات الأمان الرئيسية */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${getScoreColor(metrics.overallScore)}`}>
              <ShieldCheckIcon className="w-6 h-6" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">درجة الأمان الإجمالية</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.overallScore}%</p>
              <p className="text-sm text-gray-500">{getScoreLabel(metrics.overallScore)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-blue-100 text-blue-600">
              <ClockIcon className="w-6 h-6" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">آخر دخول</p>
              <p className="text-sm font-bold text-gray-900">
                {new Date(metrics.lastLogin).toLocaleDateString('ar-SA')}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className={`p-3 rounded-full ${
              metrics.failedAttempts === 0 ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'
            }`}>
              <ExclamationTriangleIcon className="w-6 h-6" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">محاولات فاشلة</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.failedAttempts}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center">
            <div className="p-3 rounded-full bg-green-100 text-green-600">
              <DocumentDuplicateIcon className="w-6 h-6" />
            </div>
            <div className="mr-4">
              <p className="text-sm font-medium text-gray-600">النسخ الاحتياطية</p>
              <p className="text-2xl font-bold text-gray-900">{metrics.backupCount}</p>
            </div>
          </div>
        </div>
      </div>

      {/* التوصيات الأمنية */}
      {recommendations.length > 0 && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <div className="flex items-center mb-6">
            <ExclamationTriangleIcon className="w-6 h-6 text-orange-600 ml-3" />
            <h3 className="text-xl font-semibold text-gray-900">التوصيات الأمنية</h3>
          </div>

          <div className="space-y-4">
            {recommendations.map((rec) => (
              <div key={rec.id} className="flex items-start space-x-4 space-x-reverse p-4 border rounded-lg">
                <div className="flex-shrink-0">
                  {rec.completed ? (
                    <CheckCircleIcon className="w-6 h-6 text-green-500" />
                  ) : (
                    <XCircleIcon className="w-6 h-6 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900">{rec.title}</h4>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full border ${getPriorityColor(rec.priority)}`}>
                      {rec.priority === 'high' ? 'عالي' : rec.priority === 'medium' ? 'متوسط' : 'منخفض'}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                  {rec.action && !rec.completed && (
                    <button className="text-sm text-primary-600 hover:text-primary-800 mt-2">
                      {rec.action}
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
