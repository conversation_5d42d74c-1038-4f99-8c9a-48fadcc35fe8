'use client'

import { useState } from 'react'
import {
  DocumentTextIcon,
  CalculatorIcon,
  CalendarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  PlusIcon
} from '@heroicons/react/24/outline'

export default function TaxesPage() {
  const [taxRecords] = useState([
    {
      id: '1',
      year: '2024',
      type: 'ضريبة القيمة المضافة',
      amount: 15000,
      paid: 15000,
      dueDate: '2024-01-31',
      status: 'paid',
      quarter: 'Q4 2023'
    },
    {
      id: '2', 
      year: '2024',
      type: 'ضريبة الدخل',
      amount: 25000,
      paid: 0,
      dueDate: '2024-04-30',
      status: 'pending',
      quarter: '2023'
    },
    {
      id: '3',
      year: '2024',
      type: 'ضريبة القيمة المضافة',
      amount: 12000,
      paid: 0,
      dueDate: '2024-04-30',
      status: 'overdue',
      quarter: 'Q1 2024'
    }
  ])

  const totalTaxes = taxRecords.reduce((sum, tax) => sum + tax.amount, 0)
  const totalPaid = taxRecords.reduce((sum, tax) => sum + tax.paid, 0)
  const totalPending = totalTaxes - totalPaid
  const overdueCount = taxRecords.filter(tax => tax.status === 'overdue').length

  const getStatusConfig = (status: string) => {
    const configs = {
      paid: { label: 'مدفوع', color: 'text-green-600 bg-green-50', icon: CheckCircleIcon },
      pending: { label: 'معلق', color: 'text-yellow-600 bg-yellow-50', icon: ClockIcon },
      overdue: { label: 'متأخر', color: 'text-red-600 bg-red-50', icon: ExclamationTriangleIcon }
    }
    return configs[status as keyof typeof configs] || configs.pending
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">إدارة الضرائب</h1>
          <p className="text-gray-600">تتبع وإدارة التزاماتك الضريبية</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          إضافة ضريبة
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الضرائب</p>
              <p className="text-2xl font-bold text-gray-900">{totalTaxes.toLocaleString()} ر.س</p>
            </div>
            <CalculatorIcon className="w-8 h-8 text-gray-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المدفوع</p>
              <p className="text-2xl font-bold text-green-600">{totalPaid.toLocaleString()} ر.س</p>
            </div>
            <CheckCircleIcon className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">المتبقي</p>
              <p className="text-2xl font-bold text-yellow-600">{totalPending.toLocaleString()} ر.س</p>
            </div>
            <ClockIcon className="w-8 h-8 text-yellow-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">متأخرة</p>
              <p className="text-2xl font-bold text-red-600">{overdueCount}</p>
            </div>
            <ExclamationTriangleIcon className="w-8 h-8 text-red-600" />
          </div>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b">
          <h2 className="text-lg font-semibold">السجل الضريبي</h2>
        </div>
        <div className="divide-y">
          {taxRecords.map((tax) => {
            const statusConfig = getStatusConfig(tax.status)
            const StatusIcon = statusConfig.icon
            const isOverdue = tax.status === 'overdue'
            
            return (
              <div key={tax.id} className="p-6 hover:bg-gray-50 transition-colors">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <DocumentTextIcon className="w-6 h-6 text-gray-600" />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">{tax.type}</h3>
                      <p className="text-sm text-gray-500">{tax.quarter} - {tax.year}</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-6">
                    <div className="text-right">
                      <p className="text-sm text-gray-600">المبلغ المستحق</p>
                      <p className="font-medium text-gray-900">{tax.amount.toLocaleString()} ر.س</p>
                    </div>
                    
                    <div className="text-right">
                      <p className="text-sm text-gray-600">المدفوع</p>
                      <p className="font-medium text-green-600">{tax.paid.toLocaleString()} ر.س</p>
                    </div>
                    
                    <div className="text-right">
                      <p className="text-sm text-gray-600">تاريخ الاستحقاق</p>
                      <p className={`font-medium ${isOverdue ? 'text-red-600' : 'text-gray-900'}`}>
                        {tax.dueDate}
                      </p>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-medium ${statusConfig.color}`}>
                        <StatusIcon className="w-3 h-3 ml-1" />
                        {statusConfig.label}
                      </span>
                    </div>
                  </div>
                </div>
                
                {tax.status !== 'paid' && (
                  <div className="mt-4 pt-4 border-t flex justify-end">
                    <button className="btn-primary text-sm">
                      دفع الآن
                    </button>
                  </div>
                )}
              </div>
            )
          })}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h3 className="text-lg font-semibold mb-4">حاسبة الضرائب</h3>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">الدخل الشهري</label>
              <input type="number" className="w-full px-3 py-2 border border-gray-300 rounded-lg" placeholder="0" />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">نوع الضريبة</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                <option>ضريبة الدخل</option>
                <option>ضريبة القيمة المضافة</option>
              </select>
            </div>
            <button className="w-full btn-primary">
              احسب الضريبة
            </button>
          </div>
        </div>

        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-6 border border-yellow-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">📋 تذكيرات ضريبية</h3>
          <div className="space-y-3">
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">احتفظ بجميع الإيصالات والفواتير</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">قدم الإقرار الضريبي في الموعد</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">استشر محاسب مختص عند الحاجة</p>
            </div>
            <div className="flex items-start gap-3">
              <div className="w-2 h-2 bg-yellow-500 rounded-full mt-2"></div>
              <p className="text-sm text-gray-700">تابع التحديثات في القوانين الضريبية</p>
            </div>
          </div>
        </div>
      </div>

      <div className="bg-blue-50 rounded-lg p-6 border border-blue-200">
        <div className="flex items-start gap-4">
          <ExclamationTriangleIcon className="w-6 h-6 text-blue-600 mt-1" />
          <div>
            <h3 className="font-semibold text-blue-900 mb-2">تنبيه مهم</h3>
            <p className="text-sm text-blue-800">
              هذه الأدوات للمساعدة فقط. يُنصح بالتشاور مع محاسب مختص أو مستشار ضريبي للحصول على المشورة المهنية.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}