'use client'

import { useState, useEffect, FormEvent } from 'react'
import { sanitizeInput, generateCSRFToken, verifyCSRFToken } from '@/lib/security'
import { useToastContext } from '@/contexts/ToastContext'

interface SecureFormProps {
  onSubmit: (data: any, csrfToken: string) => Promise<void>
  children: React.ReactNode
  className?: string
  validateOnSubmit?: boolean
}

export default function SecureForm({ 
  onSubmit, 
  children, 
  className = '',
  validateOnSubmit = true 
}: SecureFormProps) {
  const [csrfToken, setCsrfToken] = useState<string>('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { error } = useToastContext()

  useEffect(() => {
    // إنشاء CSRF token عند تحميل النموذج
    const token = generateCSRFToken()
    setCsrfToken(token)
    sessionStorage.setItem('csrf_token', token)
  }, [])

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    
    if (isSubmitting) return
    
    setIsSubmitting(true)

    try {
      // التحقق من CSRF Token
      const storedToken = sessionStorage.getItem('csrf_token')
      if (!storedToken || !verifyCSRFToken(csrfToken, storedToken)) {
        error('خطأ في التحقق من الأمان. يرجى إعادة تحميل الصفحة.')
        return
      }

      // جمع بيانات النموذج
      const formData = new FormData(e.currentTarget)
      const data: any = {}

      // تنظيف جميع المدخلات من XSS
      for (const [key, value] of formData.entries()) {
        if (typeof value === 'string') {
          data[key] = sanitizeInput(value)
        } else {
          data[key] = value
        }
      }

      // التحقق من صحة البيانات إذا كان مطلوباً
      if (validateOnSubmit) {
        const validation = validateFormData(data)
        if (!validation.isValid) {
          error(validation.message)
          return
        }
      }

      // تنفيذ الإرسال
      await onSubmit(data, csrfToken)

      // إنشاء token جديد بعد الإرسال الناجح
      const newToken = generateCSRFToken()
      setCsrfToken(newToken)
      sessionStorage.setItem('csrf_token', newToken)

    } catch (err) {
      console.error('خطأ في إرسال النموذج:', err)
      error('حدث خطأ في إرسال النموذج. يرجى المحاولة مرة أخرى.')
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className={className}>
      {/* CSRF Token المخفي */}
      <input type="hidden" name="csrf_token" value={csrfToken} />
      
      {/* محتوى النموذج */}
      {children}
      
      {/* حالة الإرسال */}
      {isSubmitting && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="flex items-center space-x-2">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary-600"></div>
            <span className="text-sm text-gray-600">جاري الإرسال...</span>
          </div>
        </div>
      )}
    </form>
  )
}

// التحقق من صحة البيانات الأساسية
function validateFormData(data: any): { isValid: boolean; message: string } {
  // التحقق من الحقول المطلوبة
  const requiredFields = ['email', 'password']
  
  for (const field of requiredFields) {
    if (data[field] && typeof data[field] === 'string') {
      if (data[field].trim().length === 0) {
        return {
          isValid: false,
          message: `الحقل ${field} مطلوب`
        }
      }
    }
  }

  // التحقق من البريد الإلكتروني
  if (data.email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      return {
        isValid: false,
        message: 'البريد الإلكتروني غير صحيح'
      }
    }
  }

  // التحقق من كلمة المرور
  if (data.password && data.password.length < 8) {
    return {
      isValid: false,
      message: 'كلمة المرور يجب أن تكون 8 أحرف على الأقل'
    }
  }

  // التحقق من الأرقام
  if (data.phone) {
    const phoneRegex = /^[0-9+\-\s()]+$/
    if (!phoneRegex.test(data.phone)) {
      return {
        isValid: false,
        message: 'رقم الهاتف غير صحيح'
      }
    }
  }

  // التحقق من المبالغ المالية
  if (data.amount) {
    const amount = parseFloat(data.amount)
    if (isNaN(amount) || amount < 0) {
      return {
        isValid: false,
        message: 'المبلغ يجب أن يكون رقماً موجباً'
      }
    }
  }

  return { isValid: true, message: '' }
}

// مكون إدخال آمن
interface SecureInputProps {
  type?: string
  name: string
  placeholder?: string
  required?: boolean
  className?: string
  maxLength?: number
  minLength?: number
  pattern?: string
  value?: string
  onChange?: (value: string) => void
}

export function SecureInput({
  type = 'text',
  name,
  placeholder,
  required = false,
  className = '',
  maxLength,
  minLength,
  pattern,
  value,
  onChange
}: SecureInputProps) {
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const sanitizedValue = sanitizeInput(e.target.value)
    if (onChange) {
      onChange(sanitizedValue)
    }
  }

  return (
    <input
      type={type}
      name={name}
      placeholder={placeholder}
      required={required}
      className={`
        block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-primary-500 focus:border-primary-500
        ${className}
      `}
      maxLength={maxLength}
      minLength={minLength}
      pattern={pattern}
      value={value}
      onChange={handleChange}
      autoComplete="off"
      spellCheck="false"
    />
  )
}

// مكون نص آمن
interface SecureTextAreaProps {
  name: string
  placeholder?: string
  required?: boolean
  className?: string
  maxLength?: number
  rows?: number
  value?: string
  onChange?: (value: string) => void
}

export function SecureTextArea({
  name,
  placeholder,
  required = false,
  className = '',
  maxLength = 1000,
  rows = 4,
  value,
  onChange
}: SecureTextAreaProps) {
  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const sanitizedValue = sanitizeInput(e.target.value)
    if (onChange) {
      onChange(sanitizedValue)
    }
  }

  return (
    <textarea
      name={name}
      placeholder={placeholder}
      required={required}
      className={`
        block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm
        focus:outline-none focus:ring-primary-500 focus:border-primary-500
        resize-vertical
        ${className}
      `}
      maxLength={maxLength}
      rows={rows}
      value={value}
      onChange={handleChange}
      spellCheck="false"
    />
  )
}
