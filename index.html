<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>متابعة مالية - المعاينة</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        .logo {
            width: 80px;
            height: 80px;
            background: #0284c7;
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
            margin: 0 auto 20px;
        }
        h1 {
            color: #1f2937;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .subtitle {
            color: #6b7280;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .status {
            background: #f0f9ff;
            border: 2px solid #0284c7;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        .status h2 {
            color: #0284c7;
            margin: 0 0 10px 0;
            font-size: 20px;
        }
        .commands {
            background: #f9fafb;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        .command {
            background: #374151;
            color: white;
            padding: 10px 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 5px 0;
            display: block;
            direction: ltr;
            text-align: left;
        }
        .feature {
            display: flex;
            align-items: center;
            margin: 15px 0;
            text-align: right;
        }
        .feature-icon {
            width: 40px;
            height: 40px;
            background: #e0f2fe;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 15px;
            font-size: 20px;
        }
        .btn {
            background: #0284c7;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        .btn:hover {
            background: #0369a1;
        }
        .btn-secondary {
            background: #6b7280;
        }
        .btn-secondary:hover {
            background: #4b5563;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">م</div>
        <h1>موقع متابعة مالية</h1>
        <p class="subtitle">منصة الإدارة المالية الذكية - مستلهمة من موقع أبشر</p>
        
        <div class="status">
            <h2>🚀 المشروع جاهز للتشغيل!</h2>
            <p>تم إنشاء جميع الملفات والمكونات المطلوبة بنجاح</p>
        </div>

        <div class="commands">
            <h3>خطوات التشغيل:</h3>
            <strong>الطريقة الأولى (سريعة):</strong>
            <code class="command">./start.bat</code>
            <small style="display: block; margin-top: 5px; color: #6b7280;">أو start.sh للأنظمة الأخرى</small>
            
            <br><strong>الطريقة الثانية (يدوية):</strong>
            <code class="command">npm install</code>
            <code class="command">npm run dev</code>
            
            <p style="margin-top: 15px; color: #059669;">
                ✅ بعد التشغيل، افتح: <strong>http://localhost:3000</strong>
            </p>
        </div>

        <div style="text-align: right; margin: 20px 0;">
            <h3>المميزات المتوفرة:</h3>
            
            <div class="feature">
                <div class="feature-icon">🏠</div>
                <div>
                    <strong>صفحة رئيسية تفاعلية</strong>
                    <br><small>تصميم مستلهم من موقع أبشر بالكامل</small>
                </div>
            </div>

            <div class="feature">
                <div class="feature-icon">🔐</div>
                <div>
                    <strong>صفحة تسجيل دخول</strong>
                    <br><small>واجهة آمنة وسهلة الاستخدام</small>
                </div>
            </div>

            <div class="feature">
                <div class="feature-icon">📊</div>
                <div>
                    <strong>لوحة تحكم ذكية</strong>
                    <br><small>إحصائيات وتقارير مالية مفصلة</small>
                </div>
            </div>

            <div class="feature">
                <div class="feature-icon">💳</div>
                <div>
                    <strong>إدارة الفواتير</strong>
                    <br><small>رفع وتتبع الفواتير بسهولة</small>
                </div>
            </div>

            <div class="feature">
                <div class="feature-icon">📝</div>
                <div>
                    <strong>الطلبات المالية</strong>
                    <br><small>طلب سلف وخدمات مالية متنوعة</small>
                </div>
            </div>

            <div class="feature">
                <div class="feature-icon">📁</div>
                <div>
                    <strong>إدارة المستندات</strong>
                    <br><small>رفع وأرشفة آمنة للمستندات</small>
                </div>
            </div>
        </div>

        <div style="margin-top: 30px;">
            <a href="README.md" class="btn">📖 قراءة الوثائق</a>
            <a href="DEVELOPMENT.md" class="btn btn-secondary">🛠️ دليل التطوير</a>
        </div>

        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb; color: #6b7280; font-size: 14px;">
            <p><strong>تقنيات المشروع:</strong> Next.js 14 • TypeScript • Tailwind CSS • Heroicons</p>
            <p><strong>المميزات:</strong> دعم كامل للعربية • تصميم متجاوب • أمان عالي • تجربة مستخدم متميزة</p>
        </div>
    </div>
</body>
</html>