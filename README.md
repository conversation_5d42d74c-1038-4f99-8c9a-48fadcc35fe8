# متابعة مالية - منصة الإدارة المالية الذكية

منصة متابعة مالية هي موقع إلكتروني احترافي مستلهم من تصميم موقع "أبشر" يهدف إلى تمكين الأفراد والشركات من إدارة ومتابعة وضعهم المالي بدقة وأمان.

## 🌟 المميزات الرئيسية

- **واجهة مستخدم عربية بالكامل** مع دعم RTL
- **تصميم مستلهم من أبشر** للسهولة والألفة
- **لوحة تحكم ذكية** مع إحصائيات فورية
- **تقارير مالية مفصلة** مع تحليل ذكي
- **إدارة الفواتير والمستندات**
- **نظام طلبات مالية** (سلف، خدمات مالية)
- **أمان عالي** مع تشفير البيانات
- **تصميم متجاوب** يدعم جميع الأجهزة

## 🚀 التقنيات المستخدمة

- **Frontend:** Next.js 14 مع TypeScript
- **Styling:** Tailwind CSS
- **Icons:** Heroicons
- **Fonts:** Google Fonts (خط تجول)
- **Components:** Headless UI

## 📋 متطلبات التشغيل

- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

## ⚡ التثبيت والتشغيل

### 1. تثبيت الحزم المطلوبة

```bash
npm install
```

أو

```bash
yarn install
```

### 2. تشغيل الخادم المحلي

```bash
npm run dev
```

أو

```bash
yarn dev
```

### 3. فتح المتصفح

انتقل إلى [http://localhost:3000](http://localhost:3000) لرؤية الموقع.

## 📱 هيكل المشروع

```
├── app/
│   ├── components/          # المكونات المشتركة
│   │   ├── Header.tsx       # شريط التنقل العلوي
│   │   └── Footer.tsx       # تذييل الصفحة
│   ├── auth/
│   │   └── login/           # صفحة تسجيل الدخول
│   ├── dashboard/           # لوحة التحكم
│   │   ├── reports/         # التقارير المالية
│   │   ├── invoices/        # إدارة الفواتير
│   │   ├── requests/        # الطلبات المالية
│   │   ├── documents/       # المستندات
│   │   ├── history/         # سجل العمليات
│   │   └── settings/        # الإعدادات
│   ├── globals.css          # الأنماط العامة
│   ├── layout.tsx           # التخطيط الرئيسي
│   └── page.tsx             # الصفحة الرئيسية
├── public/                  # الملفات العامة
├── tailwind.config.js       # إعدادات Tailwind
├── next.config.js           # إعدادات Next.js
└── package.json             # معلومات المشروع
```

## 🎨 تخصيص التصميم

### الألوان الرئيسية
- **الأزرق الأساسي:** `#0284c7` (primary-600)
- **الأخضر:** للنجاح والعمليات المكتملة
- **الأحمر:** للتحذيرات والأخطاء
- **البرتقالي:** للعمليات قيد المراجعة

### الخطوط
- **الخط الأساسي:** Tajawal (مخصص للعربية)
- **خط احتياطي:** system-ui, sans-serif

## 🔐 الأمان

- تشفير جميع البيانات الحساسة
- التحقق من صحة المدخلات
- حماية ضد هجمات XSS و CSRF
- جلسات آمنة مع JWT

## 📊 الخدمات المتاحة

### 1. إدارة الفواتير
- رفع وتصنيف الفواتير
- تتبع حالة الدفع
- تذكيرات الاستحقاق

### 2. التقارير المالية
- تحليل الإنفاق الشهري
- تصنيف المصروفات
- توقع الميزانية

### 3. الطلبات المالية
- طلب سلفة
- طلب تحليل مالي
- خدمات مالية أخرى

### 4. إدارة المستندات
- رفع وأرشفة المستندات
- استخراج البيانات بالذكاء الاصطناعي
- تصنيف تلقائي

## 🤖 الذكاء الاصطناعي (مستقبلياً)

- **تحليل تلقائي للتقارير المالية**
- **توقع المصاريف القادمة**
- **روبوت محادثة ذكي**
- **استخلاص البيانات من المستندات**

## 🚀 النشر على الإنتاج

### Vercel (مُوصى به)

```bash
npm run build
```

ثم ارفع المشروع على Vercel

### خادم مخصص

```bash
npm run build
npm start
```

## 🛠️ التطوير

### إضافة صفحة جديدة
1. أنشئ مجلد جديد في `app/`
2. أضف ملف `page.tsx`
3. اختياري: أضف `layout.tsx` للتخطيط المخصص

### إضافة مكون جديد
1. أنشإ الملف في `app/components/`
2. استخدم TypeScript و Tailwind CSS
3. تأكد من دعم RTL

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push للبرانش
5. فتح Pull Request

## 📞 الدعم

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +966 11 123 4567

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🔄 التحديثات

- **v1.0.0** - الإصدار الأولي
- **v1.1.0** - إضافة التقارير المالية
- **v1.2.0** - تحسين الأمان وواجهة المستخدم

---

تم التطوير بـ ❤️ لخدمة المجتمع العربي في إدارة الأمور المالية بسهولة وأمان.