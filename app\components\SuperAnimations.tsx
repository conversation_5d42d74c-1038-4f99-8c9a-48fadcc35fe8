'use client'

import { useEffect, useRef, useState } from 'react'

// مكون الجسيمات المتحركة
export function FloatingParticles({ count = 20, color = 'electric' }: { count?: number, color?: string }) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [particles, setParticles] = useState<Array<{ id: number, x: number, y: number, delay: number }>>([])

  useEffect(() => {
    const newParticles = Array.from({ length: count }, (_, i) => ({
      id: i,
      x: Math.random() * 100,
      y: Math.random() * 100,
      delay: Math.random() * 5
    }))
    setParticles(newParticles)
  }, [count])

  const colorClasses = {
    electric: 'bg-electric-400',
    neon: 'bg-neon-400',
    cyber: 'bg-cyber-400',
    gold: 'bg-gold-400'
  }

  return (
    <div ref={containerRef} className="absolute inset-0 overflow-hidden pointer-events-none">
      {particles.map((particle) => (
        <div
          key={particle.id}
          className={`absolute w-1 h-1 ${colorClasses[color as keyof typeof colorClasses] || colorClasses.electric} rounded-full opacity-60 animate-float`}
          style={{
            left: `${particle.x}%`,
            top: `${particle.y}%`,
            animationDelay: `${particle.delay}s`,
            animationDuration: `${6 + Math.random() * 4}s`
          }}
        />
      ))}
    </div>
  )
}

// مكون التأثير المورفولوجي
export function MorphingShape({ children, className = '' }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={`morphing-card ${className}`}>
      {children}
    </div>
  )
}

// مكون التأثير اللامع
export function ShimmerEffect({ children, className = '' }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={`relative overflow-hidden ${className}`}>
      <div className="shimmer-effect absolute inset-0 z-10"></div>
      {children}
    </div>
  )
}

// مكون الوهج النابض
export function PulsingGlow({ children, color = 'electric', className = '' }: { 
  children: React.ReactNode, 
  color?: string, 
  className?: string 
}) {
  const glowColors = {
    electric: 'text-electric-500',
    neon: 'text-neon-500',
    cyber: 'text-cyber-500',
    gold: 'text-gold-500'
  }

  return (
    <div className={`pulse-glow ${glowColors[color as keyof typeof glowColors]} ${className}`}>
      {children}
    </div>
  )
}

// مكون الطفو المتفوق
export function SuperiorFloat({ children, className = '' }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={`float-superior ${className}`}>
      {children}
    </div>
  )
}

// مكون الكشف عند التمرير
export function ScrollReveal({ 
  children, 
  className = '', 
  delay = 0 
}: { 
  children: React.ReactNode, 
  className?: string, 
  delay?: number 
}) {
  const [isVisible, setIsVisible] = useState(false)
  const elementRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setTimeout(() => setIsVisible(true), delay)
        }
      },
      { threshold: 0.1 }
    )

    if (elementRef.current) {
      observer.observe(elementRef.current)
    }

    return () => observer.disconnect()
  }, [delay])

  return (
    <div 
      ref={elementRef}
      className={`scroll-reveal ${isVisible ? 'revealed' : ''} ${className}`}
    >
      {children}
    </div>
  )
}

// مكون الرفع عند التمرير
export function HoverLift({ children, className = '' }: { children: React.ReactNode, className?: string }) {
  return (
    <div className={`hover-lift ${className}`}>
      {children}
    </div>
  )
}

// مكون الخلفية المتحركة
export function AnimatedBackground({ className = '' }: { className?: string }) {
  return (
    <div className={`animated-bg ${className}`}></div>
  )
}

// مكون النص المتوهج
export function GlowText({ children, className = '' }: { children: React.ReactNode, className?: string }) {
  return (
    <span className={`text-glow ${className}`}>
      {children}
    </span>
  )
}

// مكون تأثير الكتابة
export function TypingEffect({ 
  text, 
  speed = 100, 
  className = '' 
}: { 
  text: string, 
  speed?: number, 
  className?: string 
}) {
  const [displayText, setDisplayText] = useState('')
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    if (currentIndex < text.length) {
      const timeout = setTimeout(() => {
        setDisplayText(prev => prev + text[currentIndex])
        setCurrentIndex(prev => prev + 1)
      }, speed)

      return () => clearTimeout(timeout)
    }
  }, [currentIndex, text, speed])

  return (
    <span className={`typing-effect ${className}`}>
      {displayText}
    </span>
  )
}

// مكون الزر الخارق
export function SuperButton({ 
  children, 
  onClick, 
  className = '',
  variant = 'primary'
}: { 
  children: React.ReactNode, 
  onClick?: () => void, 
  className?: string,
  variant?: 'primary' | 'secondary' | 'success' | 'warning'
}) {
  const variants = {
    primary: 'from-electric-500 to-electric-700',
    secondary: 'from-cyber-500 to-cyber-700',
    success: 'from-neon-500 to-neon-700',
    warning: 'from-gold-500 to-gold-700'
  }

  return (
    <button 
      onClick={onClick}
      className={`btn-super bg-gradient-to-r ${variants[variant]} ${className}`}
    >
      {children}
    </button>
  )
}

// مكون الكارت الخارق
export function SuperCard({ 
  children, 
  className = '',
  glowColor = 'electric'
}: { 
  children: React.ReactNode, 
  className?: string,
  glowColor?: string
}) {
  const glowColors = {
    electric: 'shadow-electric',
    neon: 'shadow-neon',
    cyber: 'shadow-cyber',
    gold: 'shadow-gold'
  }

  return (
    <div className={`card-super ${glowColors[glowColor as keyof typeof glowColors]} ${className}`}>
      <FloatingParticles count={5} color={glowColor} />
      {children}
    </div>
  )
}

// مكون التحميل الخارق
export function SuperLoading({ size = 'md', color = 'electric' }: { size?: 'sm' | 'md' | 'lg', color?: string }) {
  const sizes = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16'
  }

  const colors = {
    electric: 'bg-electric-500',
    neon: 'bg-neon-500',
    cyber: 'bg-cyber-500',
    gold: 'bg-gold-500'
  }

  return (
    <div className="flex items-center justify-center">
      <div className={`${sizes[size]} ${colors[color as keyof typeof colors]} rounded-lg super-loading`}></div>
    </div>
  )
}

// مكون الإحصائيات المتحركة
export function AnimatedCounter({ 
  end, 
  duration = 2000, 
  prefix = '', 
  suffix = '',
  className = ''
}: { 
  end: number, 
  duration?: number, 
  prefix?: string, 
  suffix?: string,
  className?: string
}) {
  const [count, setCount] = useState(0)

  useEffect(() => {
    let startTime: number
    let animationFrame: number

    const animate = (timestamp: number) => {
      if (!startTime) startTime = timestamp
      const progress = Math.min((timestamp - startTime) / duration, 1)
      
      setCount(Math.floor(progress * end))
      
      if (progress < 1) {
        animationFrame = requestAnimationFrame(animate)
      }
    }

    animationFrame = requestAnimationFrame(animate)
    
    return () => cancelAnimationFrame(animationFrame)
  }, [end, duration])

  return (
    <span className={className}>
      {prefix}{count.toLocaleString('ar-SA')}{suffix}
    </span>
  )
}

// مكون التقدم المتحرك
export function AnimatedProgress({ 
  value, 
  max = 100, 
  color = 'electric',
  className = ''
}: { 
  value: number, 
  max?: number, 
  color?: string,
  className?: string
}) {
  const percentage = (value / max) * 100

  const colors = {
    electric: 'bg-electric-500',
    neon: 'bg-neon-500',
    cyber: 'bg-cyber-500',
    gold: 'bg-gold-500'
  }

  return (
    <div className={`w-full bg-gray-200 rounded-full h-2 overflow-hidden ${className}`}>
      <div 
        className={`h-full ${colors[color as keyof typeof colors]} transition-all duration-1000 ease-out shimmer-effect`}
        style={{ width: `${percentage}%` }}
      ></div>
    </div>
  )
}
