'use client'

import { useState, useEffect } from 'react'
import { 
  ShieldCheckIcon, 
  KeyIcon, 
  EyeIcon,
  ClockIcon,
  DocumentDuplicateIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'
import ProtectedRoute from '@/components/ProtectedRoute'
import BackupManager from '@/components/BackupManager'
import SecurityDashboard from '@/components/SecurityDashboard'
import { getCurrentSession, changePassword } from '@/lib/auth'
import { useToastContext } from '@/contexts/ToastContext'
import SecureForm, { SecureInput } from '@/components/SecureForm'
import { SecurePasswordInput } from '@/components/PasswordStrengthIndicator'

interface SecurityStats {
  lastLogin: string
  loginAttempts: number
  dataEncrypted: boolean
  backupCount: number
  securityScore: number
}

export default function SecurityPage() {
  const [securityStats, setSecurityStats] = useState<SecurityStats | null>(null)
  const [showPasswordForm, setShowPasswordForm] = useState(false)
  const [securityLogs, setSecurityLogs] = useState<any[]>([])
  const { success, error } = useToastContext()

  useEffect(() => {
    loadSecurityData()
  }, [])

  const loadSecurityData = () => {
    const session = getCurrentSession()
    if (!session) return

    // تحميل إحصائيات الأمان
    const logs = JSON.parse(localStorage.getItem('security_logs') || '[]')
    const backups = JSON.parse(localStorage.getItem('financial_tracker_backups') || '[]')
    
    const loginLogs = logs.filter((log: any) => log.type === 'login')
    const failedLogins = logs.filter((log: any) => log.type === 'failed_login')
    
    const stats: SecurityStats = {
      lastLogin: loginLogs.length > 0 ? loginLogs[loginLogs.length - 1].timestamp : 'غير متوفر',
      loginAttempts: failedLogins.length,
      dataEncrypted: true, // البيانات مشفرة دائماً
      backupCount: backups.length,
      securityScore: calculateSecurityScore(logs, backups)
    }

    setSecurityStats(stats)
    setSecurityLogs(logs.slice(-10).reverse()) // آخر 10 سجلات
  }

  const calculateSecurityScore = (logs: any[], backups: any[]): number => {
    let score = 50 // نقطة البداية

    // إضافة نقاط للنسخ الاحتياطية
    score += Math.min(backups.length * 10, 30)

    // إضافة نقاط لعدم وجود محاولات دخول فاشلة
    const failedLogins = logs.filter(log => log.type === 'failed_login')
    if (failedLogins.length === 0) score += 20

    // خصم نقاط للأنشطة المشبوهة
    const suspiciousActivities = logs.filter(log => log.type === 'suspicious_activity')
    score -= suspiciousActivities.length * 5

    return Math.max(0, Math.min(100, score))
  }

  const handlePasswordChange = async (data: any) => {
    try {
      const result = await changePassword(data.currentPassword, data.newPassword)
      if (result.success) {
        success('تم تغيير كلمة المرور بنجاح')
        setShowPasswordForm(false)
        loadSecurityData()
      } else {
        error(result.message)
      }
    } catch (err) {
      error('فشل في تغيير كلمة المرور')
    }
  }

  const getSecurityScoreColor = (score: number): string => {
    if (score >= 80) return 'text-green-600 bg-green-100'
    if (score >= 60) return 'text-yellow-600 bg-yellow-100'
    return 'text-red-600 bg-red-100'
  }

  const getSecurityScoreLabel = (score: number): string => {
    if (score >= 80) return 'ممتاز'
    if (score >= 60) return 'جيد'
    return 'يحتاج تحسين'
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* العنوان الرئيسي */}
          <div className="mb-8">
            <div className="flex items-center">
              <ShieldCheckIcon className="w-8 h-8 text-primary-600 ml-3" />
              <h1 className="text-3xl font-bold text-gray-900">إعدادات الأمان</h1>
            </div>
            <p className="mt-2 text-gray-600">
              إدارة إعدادات الأمان وحماية حسابك وبياناتك المالية
            </p>
          </div>

          {/* إحصائيات الأمان */}
          {securityStats && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <div className={`p-3 rounded-full ${getSecurityScoreColor(securityStats.securityScore)}`}>
                    <ShieldCheckIcon className="w-6 h-6" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">درجة الأمان</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {securityStats.securityScore}%
                    </p>
                    <p className="text-sm text-gray-500">
                      {getSecurityScoreLabel(securityStats.securityScore)}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-blue-100 text-blue-600">
                    <ClockIcon className="w-6 h-6" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">آخر دخول</p>
                    <p className="text-sm font-bold text-gray-900">
                      {new Date(securityStats.lastLogin).toLocaleDateString('ar-SA')}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-red-100 text-red-600">
                    <ExclamationTriangleIcon className="w-6 h-6" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">محاولات فاشلة</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {securityStats.loginAttempts}
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white rounded-lg shadow-sm border p-6">
                <div className="flex items-center">
                  <div className="p-3 rounded-full bg-green-100 text-green-600">
                    <DocumentDuplicateIcon className="w-6 h-6" />
                  </div>
                  <div className="mr-4">
                    <p className="text-sm font-medium text-gray-600">النسخ الاحتياطية</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {securityStats.backupCount}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* إعدادات كلمة المرور */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center">
                  <KeyIcon className="w-6 h-6 text-primary-600 ml-3" />
                  <h2 className="text-xl font-semibold text-gray-900">كلمة المرور</h2>
                </div>
                <button
                  onClick={() => setShowPasswordForm(!showPasswordForm)}
                  className="text-sm text-primary-600 hover:text-primary-800"
                >
                  {showPasswordForm ? 'إلغاء' : 'تغيير كلمة المرور'}
                </button>
              </div>

              {showPasswordForm ? (
                <SecureForm onSubmit={handlePasswordChange}>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور الحالية
                      </label>
                      <SecureInput
                        type="password"
                        name="currentPassword"
                        required
                        className="w-full"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        كلمة المرور الجديدة
                      </label>
                      <SecurePasswordInput
                        name="newPassword"
                        placeholder="أدخل كلمة المرور الجديدة"
                        required
                        className="w-full"
                        showStrengthIndicator={true}
                        showRequirements={true}
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        تأكيد كلمة المرور الجديدة
                      </label>
                      <SecureInput
                        type="password"
                        name="confirmPassword"
                        required
                        minLength={8}
                        className="w-full"
                        placeholder="أعد إدخال كلمة المرور الجديدة"
                      />
                    </div>

                    <button
                      type="submit"
                      className="w-full bg-primary-600 text-white py-2 px-4 rounded-lg hover:bg-primary-700 transition-colors"
                    >
                      تغيير كلمة المرور
                    </button>
                  </div>
                </SecureForm>
              ) : (
                <div className="text-gray-600">
                  <p>كلمة المرور محمية ومشفرة بقوة</p>
                  <p className="text-sm mt-2">آخر تغيير: منذ أسبوع</p>
                </div>
              )}
            </div>

            {/* سجل الأنشطة الأمنية */}
            <div className="bg-white rounded-lg shadow-sm border p-6">
              <div className="flex items-center mb-6">
                <EyeIcon className="w-6 h-6 text-primary-600 ml-3" />
                <h2 className="text-xl font-semibold text-gray-900">سجل الأنشطة</h2>
              </div>

              <div className="space-y-3 max-h-64 overflow-y-auto">
                {securityLogs.length === 0 ? (
                  <p className="text-gray-500 text-center py-4">لا توجد أنشطة مسجلة</p>
                ) : (
                  securityLogs.map((log, index) => (
                    <div key={index} className="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0">
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {log.type === 'login' && 'تسجيل دخول'}
                          {log.type === 'logout' && 'تسجيل خروج'}
                          {log.type === 'failed_login' && 'فشل تسجيل دخول'}
                          {log.type === 'password_change' && 'تغيير كلمة المرور'}
                          {log.type === 'suspicious_activity' && 'نشاط مشبوه'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(log.timestamp).toLocaleString('ar-SA')}
                        </p>
                      </div>
                      <div className={`w-2 h-2 rounded-full ${
                        log.type === 'login' ? 'bg-green-500' :
                        log.type === 'logout' ? 'bg-gray-500' :
                        log.type === 'failed_login' ? 'bg-red-500' :
                        log.type === 'suspicious_activity' ? 'bg-orange-500' :
                        'bg-blue-500'
                      }`}></div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>

          {/* إدارة النسخ الاحتياطية */}
          <div className="mt-8">
            <BackupManager />
          </div>
        </div>
      </div>
    </ProtectedRoute>
  )
}
