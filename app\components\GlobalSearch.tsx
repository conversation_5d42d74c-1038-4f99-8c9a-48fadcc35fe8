'use client'

import { useState, useRef, useEffect } from 'react'
import { useData } from '@/contexts/DataContext'
import { 
  MagnifyingGlassIcon,
  DocumentTextIcon,
  CurrencyDollarIcon,
  ClockIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import Link from 'next/link'

interface SearchResult {
  id: string
  title: string
  description: string
  type: 'transaction' | 'document' | 'invoice'
  url: string
  date?: string
  amount?: number
}

export default function GlobalSearch() {
  const [isOpen, setIsOpen] = useState(false)
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const searchRef = useRef<HTMLDivElement>(null)
  const inputRef = useRef<HTMLInputElement>(null)
  const { userData } = useData()

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  useEffect(() => {
    if (query.length < 2) {
      setResults([])
      return
    }

    const searchResults: SearchResult[] = []
    
    // البحث في المعاملات
    if (userData?.transactions) {
      userData.transactions
        .filter(t => 
          t.title.toLowerCase().includes(query.toLowerCase()) ||
          t.description?.toLowerCase().includes(query.toLowerCase()) ||
          t.category.toLowerCase().includes(query.toLowerCase())
        )
        .slice(0, 5)
        .forEach(t => {
          searchResults.push({
            id: t.id,
            title: t.title,
            description: t.description || t.category,
            type: 'transaction',
            url: '/dashboard/history',
            date: t.date,
            amount: t.amount
          })
        })
    }

    // البحث في المستندات
    if (userData?.documents) {
      userData.documents
        .filter(d => 
          d.title.toLowerCase().includes(query.toLowerCase()) ||
          d.category.toLowerCase().includes(query.toLowerCase()) ||
          d.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
        )
        .slice(0, 5)
        .forEach(d => {
          searchResults.push({
            id: d.id,
            title: d.title,
            description: d.category,
            type: 'document',
            url: '/dashboard/documents',
            date: d.uploadDate
          })
        })
    }

    setResults(searchResults)
  }, [query, userData])

  const getResultIcon = (type: string) => {
    switch (type) {
      case 'transaction':
        return <CurrencyDollarIcon className="w-5 h-5 text-green-500" />
      case 'document':
        return <DocumentTextIcon className="w-5 h-5 text-blue-500" />
      case 'invoice':
        return <DocumentTextIcon className="w-5 h-5 text-purple-500" />
      default:
        return <MagnifyingGlassIcon className="w-5 h-5 text-gray-500" />
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'transaction':
        return 'معاملة'
      case 'document':
        return 'مستند'
      case 'invoice':
        return 'فاتورة'
      default:
        return ''
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      setIsOpen(false)
      inputRef.current?.blur()
    }
  }

  return (
    <div className="relative flex-1 max-w-lg" ref={searchRef}>
      <div className="relative">
        <MagnifyingGlassIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
        <input
          ref={inputRef}
          type="text"
          placeholder="البحث في جميع البيانات..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onFocus={() => setIsOpen(true)}
          onKeyDown={handleKeyDown}
          className="w-full pl-4 pr-10 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
        />
        {query && (
          <button
            onClick={() => {
              setQuery('')
              setResults([])
            }}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        )}
      </div>

      {isOpen && (query.length >= 2 || results.length > 0) && (
        <div className="absolute top-full left-0 right-0 mt-2 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
          {results.length > 0 ? (
            <>
              <div className="p-3 border-b border-gray-200">
                <p className="text-sm text-gray-600">
                  تم العثور على {results.length} نتيجة
                </p>
              </div>
              {results.map((result) => (
                <Link
                  key={result.id}
                  href={result.url}
                  onClick={() => setIsOpen(false)}
                  className="block p-4 hover:bg-gray-50 transition-colors border-b border-gray-100 last:border-b-0"
                >
                  <div className="flex items-center space-x-3 space-x-reverse">
                    <div className="flex-shrink-0">
                      {getResultIcon(result.type)}
                    </div>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {result.title}
                        </p>
                        <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                          {getTypeLabel(result.type)}
                        </span>
                      </div>
                      <p className="text-sm text-gray-600 truncate mt-1">
                        {result.description}
                      </p>
                      <div className="flex items-center justify-between mt-2">
                        {result.date && (
                          <div className="flex items-center text-xs text-gray-500">
                            <ClockIcon className="w-3 h-3 ml-1" />
                            {result.date}
                          </div>
                        )}
                        {result.amount !== undefined && (
                          <span className={`text-sm font-medium ${
                            result.amount > 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {result.amount > 0 ? '+' : ''}{result.amount} ر.س
                          </span>
                        )}
                      </div>
                    </div>
                    <ArrowRightIcon className="w-4 h-4 text-gray-400" />
                  </div>
                </Link>
              ))}
            </>
          ) : query.length >= 2 ? (
            <div className="p-8 text-center">
              <MagnifyingGlassIcon className="w-12 h-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">لم يتم العثور على نتائج</p>
              <p className="text-sm text-gray-400 mt-1">
                جرب البحث بكلمات مختلفة
              </p>
            </div>
          ) : (
            <div className="p-4">
              <p className="text-sm text-gray-500 text-center">
                ابدأ بكتابة حرفين على الأقل للبحث
              </p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}