'use client'

import { useState } from 'react'
import { useToastContext } from '@/contexts/ToastContext'
import {
  PlusIcon,
  StarIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  TrophyIcon,
  HomeIcon,
  AcademicCapIcon,
  HeartIcon
} from '@heroicons/react/24/outline'

export default function GoalsPage() {
  const { success } = useToastContext()
  const [goals, setGoals] = useState([
    { 
      id: '1', 
      title: 'شراء منزل', 
      target: 500000, 
      current: 150000, 
      deadline: '2026-12-31',
      category: 'سكن',
      icon: HomeIcon,
      color: 'blue'
    },
    { 
      id: '2', 
      title: 'تعليم الأطفال', 
      target: 100000, 
      current: 35000, 
      deadline: '2028-09-01',
      category: 'تعليم',
      icon: AcademicCapIcon,
      color: 'green'
    },
    { 
      id: '3', 
      title: 'صندوق الطوارئ', 
      target: 50000, 
      current: 42000, 
      deadline: '2025-06-30',
      category: 'طوارئ',
      icon: HeartIcon,
      color: 'red'
    },
    { 
      id: '4', 
      title: 'رحلة العمرة', 
      target: 15000, 
      current: 8000, 
      deadline: '2025-03-15',
      category: 'سفر',
      icon: StarIcon,
      color: 'purple'
    }
  ])

  const totalTargets = goals.reduce((sum, g) => sum + g.target, 0)
  const totalSaved = goals.reduce((sum, g) => sum + g.current, 0)
  const completedGoals = goals.filter(g => g.current >= g.target).length

  const getColorClasses = (color: string) => {
    const colors = {
      blue: 'text-blue-600 bg-blue-50 border-blue-200',
      green: 'text-green-600 bg-green-50 border-green-200',
      red: 'text-red-600 bg-red-50 border-red-200',
      purple: 'text-purple-600 bg-purple-50 border-purple-200'
    }
    return colors[color as keyof typeof colors] || colors.blue
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">الأهداف المالية</h1>
          <p className="text-gray-600">حدد أهدافك المالية وتتبع تقدمك</p>
        </div>
        <button className="btn-primary flex items-center gap-2">
          <PlusIcon className="w-4 h-4" />
          هدف جديد
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي الأهداف</p>
              <p className="text-2xl font-bold text-gray-900">{goals.length}</p>
            </div>
            <StarIcon className="w-8 h-8 text-yellow-500" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">أهداف مكتملة</p>
              <p className="text-2xl font-bold text-green-600">{completedGoals}</p>
            </div>
            <TrophyIcon className="w-8 h-8 text-green-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المدخر</p>
              <p className="text-2xl font-bold text-blue-600">{totalSaved.toLocaleString()} ر.س</p>
            </div>
            <CurrencyDollarIcon className="w-8 h-8 text-blue-600" />
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-sm border">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm text-gray-600">إجمالي المطلوب</p>
              <p className="text-2xl font-bold text-purple-600">{totalTargets.toLocaleString()} ر.س</p>
            </div>
            <CalendarIcon className="w-8 h-8 text-purple-600" />
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {goals.map((goal) => {
          const percentage = (goal.current / goal.target) * 100
          const isCompleted = goal.current >= goal.target
          const IconComponent = goal.icon
          const daysLeft = Math.ceil((new Date(goal.deadline).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))
          
          return (
            <div key={goal.id} className={`bg-white rounded-lg shadow-sm border-2 ${getColorClasses(goal.color)} p-6`}>
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getColorClasses(goal.color)}`}>
                    <IconComponent className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{goal.title}</h3>
                    <p className="text-sm text-gray-500">{goal.category}</p>
                  </div>
                </div>
                {isCompleted && <TrophyIcon className="w-6 h-6 text-yellow-500" />}
              </div>

              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span>المدخر: {goal.current.toLocaleString()} ر.س</span>
                  <span>الهدف: {goal.target.toLocaleString()} ر.س</span>
                </div>

                <div className="w-full bg-gray-200 rounded-full h-3">
                  <div 
                    className={`h-3 rounded-full transition-all duration-300 ${
                      isCompleted ? 'bg-green-500' : `bg-${goal.color}-500`
                    }`}
                    style={{ width: `${Math.min(percentage, 100)}%` }}
                  ></div>
                </div>

                <div className="flex justify-between items-center text-sm">
                  <span className="font-medium">{percentage.toFixed(1)}% مكتمل</span>
                  <span className={`${daysLeft < 30 ? 'text-red-600' : 'text-gray-500'}`}>
                    {daysLeft > 0 ? `${daysLeft} يوم متبقي` : 'انتهى الموعد'}
                  </span>
                </div>

                <div className="pt-3 border-t">
                  <div className="flex justify-between items-center">
                    <span className="text-sm text-gray-600">
                      متبقي: {(goal.target - goal.current).toLocaleString()} ر.س
                    </span>
                    <button className="text-sm text-blue-600 hover:text-blue-700 font-medium">
                      إضافة مبلغ
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 border border-blue-200">
        <h2 className="text-lg font-semibold text-gray-900 mb-4">💡 نصائح لتحقيق أهدافك</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <p className="text-sm text-gray-700">حدد مبلغاً ثابتاً للادخار شهرياً</p>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <p className="text-sm text-gray-700">راجع أهدافك وحدثها بانتظام</p>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <p className="text-sm text-gray-700">ابدأ بالأهداف الصغيرة أولاً</p>
          </div>
          <div className="flex items-start gap-3">
            <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
            <p className="text-sm text-gray-700">احتفل بإنجازاتك المالية</p>
          </div>
        </div>
      </div>
    </div>
  )
}